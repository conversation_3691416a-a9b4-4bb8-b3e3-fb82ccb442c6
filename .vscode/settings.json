{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "search.exclude": {
    "package-lock.json": true
  },

  // TypeScript
  "typescript.tsdk": "node_modules/typescript/lib", // Use the workspace version of TypeScript
  "typescript.enablePromptUseWorkspaceTsdk": true, // For security reasons it's require that users opt into using the workspace version of typescript
  "typescript.preferences.autoImportSpecifierExcludeRegexes": [
    // useRouter should be imported from `next/navigation` instead of `next/router`
    "next/router",
    // give priority for Link to next/link instead of lucide-react
    "lucide-react",
    // Not used in the project and conflicts with `use()` from React
    "chai",
    // Use Zod v4 instead of v3
    "zod$"
  ],
  "typescript.preferences.preferTypeOnlyAutoImports": true, // Prefer type-only imports

  // Vitest
  "testing.automaticallyOpenTestResults": "neverOpen", // Don't open the test results automatically

  // I18n
  "i18n-ally.localesPaths": ["src/locales"],
  "i18n-ally.keystyle": "nested",

  // Disable the default formatter, use ESLint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix with ESLint on save
  "editor.codeActionsOnSave": {
    "source.addMissingImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss",
    "github-actions-workflow"
  ]
}
