/**********************************************
 * QUIZ DATA (CATEGORIES + QUESTIONS)
 **********************************************/
const quizData = {
  attitudinal: {
    scenarioBased: [
      {
        id: 'A1',
        prompt: 'You just experienced an unexpected setback—like losing a job opportunity or failing at a big project. How do you respond?',
        options: {
          A: { text: 'Reflect and Learn', values: ['Acceptance of Suffering', 'Resilience'] },
          B: { text: 'Seek Support and Empathize', values: ['Compassion for Others', 'Humility'] },
          C: { text: 'Stay Optimistic and Plan a Comeback', values: ['Courage in Adversity', 'Hope in the Future'] },
          D: { text: 'Take a Spiritual or Mindful Approach', values: ['Faith or Spirituality', 'Detachment from Ego'] },
          E: { text: 'Offer Forgiveness and Foster Peace', values: ['Forgiveness', 'Generosity of Spirit'] },
        },
      },
      {
        id: 'A2',
        prompt: 'A disagreement arises in your team, causing tension and reduced productivity. What’s your first move?',
        options: {
          A: { text: 'Encourage Open Dialogue', values: ['Compassion for Others', 'Humility'] },
          B: { text: 'Propose a Creative Solution', values: ['Innovation and Problem-Solving', 'Leadership in a Purposeful Cause'] },
          C: { text: 'Assign Clear Responsibilities', values: ['Responsibility and Duty', 'Professional Achievement'] },
          D: { text: 'Infuse Optimism and Humor', values: ['Optimism in Difficult Situations', 'Resilience'] },
          E: { text: 'Promote a Mindful Approach', values: ['Calmness and Presence', 'Hope in the Future'] },
        },
      },
      {
        id: 'A3',
        prompt: 'A challenging new role could boost your career but requires stepping out of your comfort zone. What do you do?',
        options: {
          A: { text: 'Embrace the Challenge', values: ['Courage in Adversity', 'Resilience'] },
          B: { text: 'Seek Guidance from Mentors', values: ['Mentorship and Teaching', 'Humility'] },
          C: { text: 'Weigh Risks and Benefits', values: ['Acceptance of Suffering', 'Responsibility and Duty'] },
          D: { text: 'Consider the Impact on Others', values: ['Compassion for Others', 'Deep Relationships'] },
          E: { text: 'Decline to Maintain Stability', values: ['Patience', 'Detachment from Ego'] },
        },
      },
    ],
    ratingScale: [
      {
        id: 'AR1',
        statement: 'I believe that hardship can help me grow stronger.',
        associatedValue: 'Acceptance of Suffering',
        followUp: {
          prompt: 'Share a past hardship that strengthened you.',
          options: {
            A: 'Overcoming a personal health challenge',
            B: 'Navigating a difficult relationship',
            C: 'Handling a significant career setback',
            D: 'Dealing with a major life transition',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR2',
        statement: 'I try to show compassion even to those who have hurt me.',
        associatedValue: 'Compassion for Others',
        followUp: {
          prompt: 'Describe a time when compassion guided your actions.',
          options: {
            A: 'Helping a friend through a tough time',
            B: 'Volunteering for a charitable cause',
            C: 'Mediating a conflict between others',
            D: 'Offering support to a stranger in need',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR3',
        statement: 'I face challenges head-on, even when I’m afraid.',
        associatedValue: 'Courage in Adversity',
        followUp: {
          prompt: 'Recall a situation where you demonstrated courage.',
          options: {
            A: 'Speaking up in a difficult meeting',
            B: 'Taking on a leadership role during a crisis',
            C: 'Standing up for someone being treated unfairly',
            D: 'Trying something completely new despite fear',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR4',
        statement: 'Hope for the future guides my daily actions.',
        associatedValue: 'Hope in the Future',
        followUp: {
          prompt: 'What positive outcome or future vision motivates you most?',
          options: {
            A: 'Achieving personal goals',
            B: 'Contributing to a better community',
            C: 'Building a successful career',
            D: 'Creating a supportive family environment',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR5',
        statement: 'I believe in forgiveness as a way to heal relationships.',
        associatedValue: 'Forgiveness',
        followUp: {
          prompt: 'Share an experience where forgiveness was key.',
          options: {
            A: 'Forgiving a close friend after a misunderstanding',
            B: 'Letting go of resentment towards a family member',
            C: 'Moving past a professional conflict',
            D: 'Releasing anger towards a former partner',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR6',
        statement: 'I take responsibility for my commitments and actions.',
        associatedValue: 'Responsibility and Duty',
        followUp: {
          prompt: 'Which responsibilities do you prioritize most, and why?',
          options: {
            A: 'Meeting professional deadlines',
            B: 'Maintaining personal relationships',
            C: 'Upholding ethical standards in decisions',
            D: 'Caring for family members',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR7',
        statement: 'I often keep my ego in check for the greater good.',
        associatedValue: 'Detachment from Ego',
        followUp: {
          prompt: 'When was a time you let go of personal recognition?',
          options: {
            A: 'Sharing credit with team members',
            B: 'Prioritizing team success over individual accolades',
            C: 'Avoiding boastful behavior',
            D: 'Supporting others’ ideas over my own',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR8',
        statement: 'Expressing daily gratitude is part of my routine.',
        associatedValue: 'Gratitude for Life',
        followUp: {
          prompt: 'What are you most grateful for today?',
          options: {
            A: 'Health and well-being',
            B: 'Supportive relationships',
            C: 'Personal growth and learning',
            D: 'Opportunities and experiences',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR9',
        statement: 'I remain patient, even in uncertain situations.',
        associatedValue: 'Patience',
        followUp: {
          prompt: 'Describe a moment when your patience was tested.',
          options: {
            A: 'Waiting calmly for a job offer',
            B: 'Handling delays in a project',
            C: 'Managing a lengthy personal goal',
            D: 'Supporting someone through a slow recovery',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR10',
        statement: 'I strive to stay calm and present during stressful times.',
        associatedValue: 'Calmness and Presence',
        followUp: {
          prompt: 'How do you maintain calmness in challenging situations?',
          options: {
            A: 'Practicing mindfulness or meditation',
            B: 'Engaging in physical exercise',
            C: 'Taking deep breaths and pausing before reacting',
            D: 'Seeking solitude to regain composure',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR11',
        statement: 'Generosity without expecting anything in return feels natural to me.',
        associatedValue: 'Generosity of Spirit',
        followUp: {
          prompt: 'What’s a recent act of generosity you’ve done?',
          options: {
            A: 'Donating to a charity',
            B: 'Helping a neighbor with chores',
            C: 'Volunteering at a local shelter',
            D: 'Sharing knowledge or skills with others',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR12',
        statement: 'I approach new ideas with curiosity and an open mind.',
        associatedValue: 'Curiosity and Open-Mindedness',
        followUp: {
          prompt: 'Share a new idea or concept you recently embraced.',
          options: {
            A: 'Exploring a new hobby',
            B: 'Learning a new language',
            C: 'Adopting a different perspective in a debate',
            D: 'Reading about a topic outside my expertise',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR13',
        statement: 'I value humility in my interactions with others.',
        associatedValue: 'Humility',
        followUp: {
          prompt: 'How do you demonstrate humility in your daily life?',
          options: {
            A: 'Admitting when I’m wrong',
            B: 'Listening more than speaking',
            C: 'Giving credit to others',
            D: 'Avoiding boastful behavior',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR14',
        statement: 'I strive to remain resilient in the face of challenges.',
        associatedValue: 'Resilience',
        followUp: {
          prompt: 'Describe a time when you demonstrated resilience.',
          options: {
            A: 'Bouncing back from a personal loss',
            B: 'Overcoming a significant obstacle at work',
            C: 'Maintaining positivity during tough times',
            D: 'Adapting to major life changes',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR15',
        statement: 'I prioritize maintaining a positive outlook, even when things get tough.',
        associatedValue: 'Optimism in Difficult Situations',
        followUp: {
          prompt: 'How does optimism influence your approach to challenges?',
          options: {
            A: 'Encourages me to keep trying',
            B: 'Helps me find solutions',
            C: 'Boosts my morale and motivation',
            D: 'Inspires those around me',
            E: 'Other',
          },
        },
      },
      {
        id: 'AR16',
        statement: 'I believe that maintaining faith or spirituality is important in my life.',
        associatedValue: 'Faith or Spirituality',
        followUp: {
          prompt: 'How does your faith or spirituality guide your actions?',
          options: {
            A: 'Providing strength during hardships',
            B: 'Encouraging compassionate behavior',
            C: 'Offering a sense of purpose',
            D: 'Promoting inner peace and mindfulness',
            E: 'Other',
          },
        },
      },
    ],
  },
  creative: {
    scenarioBased: [
      {
        id: 'C1',
        prompt: 'You have a free afternoon for a creative or productive pursuit. What excites you most?',
        options: {
          A: { text: 'Collaborate on a Social Project', values: ['Social Contribution', 'Mentorship and Teaching'] },
          B: { text: 'Experiment with a New Art Form', values: ['Artistic Expression', 'Innovation and Problem-Solving'] },
          C: { text: 'Focus on Career Growth / Personal Venture', values: ['Professional Achievement', 'Leadership in a Purposeful Cause'] },
          D: { text: 'Build or Craft Something', values: ['Craftsmanship', 'Building or Creating'] },
          E: { text: 'Write or Journal Ideas', values: ['Writing and Storytelling', 'Cultural Creation'] },
        },
      },
      {
        id: 'C2',
        prompt: 'You can address a community need. Your first step?',
        options: {
          A: { text: 'Brainstorm Innovative Solutions', values: ['Innovation and Problem-Solving', 'Problem-Solving for Global Issues'] },
          B: { text: 'Gather a Team', values: ['Leadership in a Purposeful Cause', 'Mentorship and Teaching'] },
          C: { text: 'Secure Funding / Resources', values: ['Professional Achievement', 'Building or Creating'] },
          D: { text: 'Design the Project Framework', values: ['Problem-Solving in Communities', 'Developing Meaningful Projects'] },
          E: { text: 'Promote via Creative Channels', values: ['Cultural Creation', 'Writing and Storytelling'] },
        },
      },
      {
        id: 'C3',
        prompt: 'You’re stuck and can’t find new ideas. How do you break the block?',
        options: {
          A: { text: 'Switch to Another Creative Outlet', values: ['Artistic Expression', 'Creative Hobbies'] },
          B: { text: 'Seek Feedback / Collaborate', values: ['Mentorship and Teaching', 'Social Contribution'] },
          C: { text: 'Take a Break & Reflect', values: ['Professional Achievement', 'Responsibility and Duty'] },
          D: { text: 'Research & Learn', values: ['Scientific Inquiry', 'Intellectual Discovery'] },
          E: { text: 'Break It Down', values: ['Professional Achievement', 'Responsibility and Duty'] },
        },
      },
    ],
    ratingScale: [
      {
        id: 'CR1',
        statement: 'I find expressing myself through art or writing deeply fulfilling.',
        associatedValue: 'Artistic Expression',
        followUp: {
          prompt: 'Describe a recent creative project you felt proud of.',
          options: {
            A: 'Creating a painting or sculpture',
            B: 'Writing a story, blog, or poetry',
            C: 'Composing music or lyrics',
            D: 'Designing a piece of digital art or graphic design',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR2',
        statement: 'Advancing my career or professional goals is important to me.',
        associatedValue: 'Professional Achievement',
        followUp: {
          prompt: 'Share a significant career milestone you reached.',
          options: {
            A: 'Earning a promotion',
            B: 'Successfully leading a major project',
            C: 'Gaining a new certification or degree',
            D: 'Launching a personal business',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR3',
        statement: 'I thrive on coming up with innovative solutions for challenging problems.',
        associatedValue: 'Innovation and Problem-Solving',
        followUp: {
          prompt: 'Give an example of a problem you solved creatively.',
          options: {
            A: 'Developing a new software feature',
            B: 'Streamlining a workflow in my workplace',
            C: 'Inventing a unique product',
            D: 'Creating a novel marketing strategy',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR4',
        statement: 'I love researching or studying new topics for discovery.',
        associatedValue: 'Scientific Inquiry',
        followUp: {
          prompt: 'What subject or field fascinates you right now?',
          options: {
            A: 'Exploring artificial intelligence',
            B: 'Studying environmental science',
            C: 'Delving into human psychology',
            D: 'Learning about ancient civilizations',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR5',
        statement: 'Creating tangible projects (like building or crafting) brings me satisfaction.',
        associatedValue: 'Building or Creating',
        followUp: {
          prompt: 'What’s something you built or crafted that was meaningful to you?',
          options: {
            A: 'Building furniture or DIY home projects',
            B: 'Crafting handmade jewelry or decorations',
            C: 'Assembling a model or kit',
            D: 'Creating a piece of wearable art',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR6',
        statement: 'Helping others learn (through mentorship/teaching) is fulfilling.',
        associatedValue: 'Mentorship and Teaching',
        followUp: {
          prompt: 'Who have you mentored, and how did it feel?',
          options: {
            A: 'Mentoring a junior colleague',
            B: 'Teaching a skill or subject to someone',
            C: 'Coaching a sports team',
            D: 'Guiding a student through a project',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR7',
        statement: 'Designing solutions for communal challenges is a priority for me.',
        associatedValue: 'Problem-Solving in Communities',
        followUp: {
          prompt: 'Which community issue have you tried to address?',
          options: {
            A: 'Improving local recycling programs',
            B: 'Enhancing public transportation',
            C: 'Creating community gardens',
            D: 'Organizing neighborhood safety initiatives',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR8',
        statement: 'Volunteering or activism that contributes to society feels purposeful.',
        associatedValue: 'Social Contribution',
        followUp: {
          prompt: 'Describe a social contribution you’ve made recently.',
          options: {
            A: 'Volunteering at a food bank',
            B: 'Participating in a fundraising event',
            C: 'Leading a community clean-up',
            D: 'Advocating for a social cause',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR9',
        statement: 'Leading an initiative that aligns with my values motivates me.',
        associatedValue: 'Leadership in a Purposeful Cause',
        followUp: {
          prompt: 'What cause or purpose have you led others in?',
          options: {
            A: 'Organizing a charity fundraiser',
            B: 'Leading a sustainability project',
            C: 'Managing a local arts initiative',
            D: 'Spearheading a health awareness campaign',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR10',
        statement: 'I enjoy creative hobbies like crafting, playing music, or writing regularly.',
        associatedValue: 'Creative Hobbies',
        followUp: {
          prompt: 'Which creative hobby do you do most often?',
          options: {
            A: 'Playing a musical instrument',
            B: 'Engaging in crafting or DIY projects',
            C: 'Writing regularly (journals, stories)',
            D: 'Painting or drawing',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR11',
        statement: 'Raising or guiding children (or the next generation) is important to me.',
        associatedValue: 'Raising Children',
        followUp: {
          prompt: 'Explain why nurturing younger individuals matters to you.',
          options: {
            A: 'Teaching life skills',
            B: 'Providing emotional support',
            C: 'Encouraging educational pursuits',
            D: 'Modeling positive behavior',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR12',
        statement: 'Developing long-term, meaningful projects energizes me.',
        associatedValue: 'Developing Meaningful Projects',
        followUp: {
          prompt: 'What ongoing project are you most passionate about?',
          options: {
            A: 'Building a non-profit organization',
            B: 'Creating a long-term art project',
            C: 'Developing a personal business',
            D: 'Working on a community development initiative',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR13',
        statement: 'I feel drawn to solving global issues, like climate change or poverty.',
        associatedValue: 'Problem-Solving for Global Issues',
        followUp: {
          prompt: 'Which global problem resonates most with you, and why?',
          options: {
            A: 'Combating climate change',
            B: 'Addressing global poverty',
            C: 'Promoting global education',
            D: 'Enhancing healthcare access',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR14',
        statement: 'Producing cultural works (books, films, music) that spark conversation excites me.',
        associatedValue: 'Cultural Creation',
        followUp: {
          prompt: 'Have you created (or do you plan to create) any cultural content? What is it?',
          options: {
            A: 'Writing a novel or memoir',
            B: 'Producing a short film or documentary',
            C: 'Composing music or creating a podcast',
            D: 'Designing an art exhibition',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR15',
        statement: 'Building physical objects with care and skill (craftsmanship) is rewarding.',
        associatedValue: 'Craftsmanship',
        followUp: {
          prompt: 'What’s the last thing you built or made with your hands?',
          options: {
            A: 'Crafting handmade furniture',
            B: 'Creating a sculpture',
            C: 'Assembling a complex model',
            D: 'Building electronics or gadgets',
            E: 'Other',
          },
        },
      },
      {
        id: 'CR16',
        statement: 'Writing and storytelling feel like part of my personal mission.',
        associatedValue: 'Writing and Storytelling',
        followUp: {
          prompt: 'What story or idea do you most want to share?',
          options: {
            A: 'Sharing personal experiences through writing',
            B: 'Creating fictional narratives',
            C: 'Blogging about topics I’m passionate about',
            D: 'Developing educational content',
            E: 'Other',
          },
        },
      },
    ],
  },
  experiential: {
    scenarioBased: [
      {
        id: 'E1',
        prompt: 'You’ve been given a week off with all expenses paid. How do you spend it?',
        options: {
          A: { text: 'Explore Nature', values: ['Connection with Nature', 'Moments of Awe'] },
          B: { text: 'Immerse in Local Cultures', values: ['Cultural Immersion', 'Intellectual Discovery'] },
          C: { text: 'Attend a Spiritual Retreat', values: ['Spiritual Experiences', 'Emotional Connection'] },
          D: { text: 'Travel with Loved Ones', values: ['Deep Relationships', 'Shared Experiences'] },
          E: { text: 'Unplug & Savor Quiet Moments', values: ['Enjoyment of Simple Moments', 'Positive Reflection'] },
        },
      },
      {
        id: 'E2',
        prompt: 'It’s a beautiful Saturday morning. How do you choose to spend your free time?',
        options: {
          A: { text: 'Hiking a New Trail', values: ['Adventure and Exploration', 'Connection with Nature'] },
          B: { text: 'Visiting an Art Exhibition', values: ['Aesthetic Appreciation', 'Intellectual Discovery'] },
          C: { text: 'Organizing a Family Gathering', values: ['Deep Relationships', 'Shared Experiences'] },
          D: { text: 'Engaging in a Mindful Practice', values: ['Calmness and Presence', 'Spiritual Experiences'] },
          E: { text: 'Trying a New Cuisine', values: ['Sensory Experiences', 'Cultural Immersion'] },
        },
      },
      {
        id: 'E3',
        prompt: 'After finishing a successful project, how do you celebrate your achievement?',
        options: {
          A: { text: 'Host a Team Celebration', values: ['Shared Experiences', 'Deep Relationships'] },
          B: { text: 'Spend Time in Nature', values: ['Connection with Nature', 'Moments of Awe'] },
          C: { text: 'Create a Personal Memento', values: ['Positive Reflection', 'Enjoyment of Simple Moments'] },
          D: { text: 'Volunteer for a Cause', values: ['Social Contribution', 'Emotional Connection'] },
          E: { text: 'Indulge a Favorite Hobby', values: ['Creative Hobbies', 'Enjoyment of Simple Moments'] },
        },
      },
    ],
    ratingScale: [
      {
        id: 'ER1',
        statement: 'Building and maintaining deep relationships is crucial to my happiness.',
        associatedValue: 'Deep Relationships',
        followUp: {
          prompt: 'What qualities make a relationship feel ‘deep’ or truly connected to you?',
          options: {
            A: 'Mutual trust and honesty',
            B: 'Shared experiences and memories',
            C: 'Emotional support and understanding',
            D: 'Open communication and respect',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER2',
        statement: 'Spending time in nature recharges me in a unique way.',
        associatedValue: 'Connection with Nature',
        followUp: {
          prompt: 'Describe your most memorable nature experience.',
          options: {
            A: 'Hiking in the mountains',
            B: 'Camping under the stars',
            C: 'Walking through a serene forest',
            D: 'Relaxing by a tranquil lake',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER3',
        statement: 'I love admiring art, architecture, or natural beauty.',
        associatedValue: 'Aesthetic Appreciation',
        followUp: {
          prompt: 'What recent aesthetic experience inspired you?',
          options: {
            A: 'Visiting an art gallery',
            B: 'Observing a stunning sunset',
            C: 'Exploring architectural marvels',
            D: 'Enjoying a beautiful garden',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER4',
        statement: 'Immersing myself in different cultures broadens my perspective.',
        associatedValue: 'Cultural Immersion',
        followUp: {
          prompt: 'Share a time when exploring a new culture changed your viewpoint.',
          options: {
            A: 'Traveling to a foreign country',
            B: 'Participating in a cultural festival',
            C: 'Trying diverse cuisines',
            D: 'Learning a new language',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER5',
        statement: 'Savoring delicious food, music, or tactile sensations brings me joy.',
        associatedValue: 'Sensory Experiences',
        followUp: {
          prompt: 'Which sensory experience brought you the most joy recently?',
          options: {
            A: 'Enjoying a gourmet meal',
            B: 'Listening to live music',
            C: 'Feeling the textures in a craft project',
            D: 'Savoring a favorite dessert',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER6',
        statement: 'Appreciating simple daily moments like morning coffee or quiet evenings is meaningful.',
        associatedValue: 'Enjoyment of Simple Moments',
        followUp: {
          prompt: 'Which simple daily ritual do you cherish the most?',
          options: {
            A: 'Drinking morning coffee quietly',
            B: 'Reading a book before bed',
            C: 'Enjoying a peaceful walk',
            D: 'Meditating or journaling',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER7',
        statement: 'Trying new activities or exploring new places excites me.',
        associatedValue: 'Adventure and Exploration',
        followUp: {
          prompt: 'What’s a recent adventure you’ve had?',
          options: {
            A: 'Skydiving or bungee jumping',
            B: 'Exploring a new city or neighborhood',
            C: 'Trying a new sport or physical activity',
            D: 'Attending a workshop or class outside my comfort zone',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER8',
        statement: 'Learning and discovering new ideas (through reading, lectures, etc.) energizes me.',
        associatedValue: 'Intellectual Discovery',
        followUp: {
          prompt: 'What topic or field fascinates you right now?',
          options: {
            A: 'Advances in technology',
            B: 'Environmental science',
            C: 'Human psychology',
            D: 'Ancient history',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER9',
        statement: 'Sharing experiences (concerts, festivals, gatherings) fosters a sense of connection for me.',
        associatedValue: 'Shared Experiences',
        followUp: {
          prompt: 'Describe a shared event that was deeply meaningful to you.',
          options: {
            A: 'Attending a music concert',
            B: 'Participating in a local festival',
            C: 'Hosting a family reunion',
            D: 'Joining a community workshop',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER10',
        statement: 'Observing nature’s cycles (changing seasons, life cycles) is calming and inspiring.',
        associatedValue: 'Appreciation of Nature’s Cycles',
        followUp: {
          prompt: 'What aspect of nature’s cycles do you find most profound?',
          options: {
            A: 'Watching the changing seasons',
            B: 'Observing plant growth and blooming',
            C: 'Monitoring weather patterns',
            D: 'Witnessing animal migrations',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER11',
        statement: 'Feeling a strong emotional bond (with people, animals, causes) is essential to my fulfillment.',
        associatedValue: 'Emotional Connection',
        followUp: {
          prompt: 'What emotional connection do you value the most in your life?',
          options: {
            A: 'Bond with family members',
            B: 'Friendship bonds',
            C: 'Connection with pets',
            D: 'Commitment to a cause or community',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER12',
        statement: 'Moments of awe (like stargazing or mountain views) fill me with wonder.',
        associatedValue: 'Moments of Awe',
        followUp: {
          prompt: 'When was the last time you felt pure awe?',
          options: {
            A: 'Stargazing on a clear night',
            B: 'Standing before a majestic mountain',
            C: 'Witnessing a solar eclipse',
            D: 'Seeing a breathtaking waterfall',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER13',
        statement: 'Engaging in spiritual or transcendent practices enriches my life.',
        associatedValue: 'Spiritual Experiences',
        followUp: {
          prompt: 'Describe a spiritual or meditative experience that was significant to you.',
          options: {
            A: 'Attending a meditation retreat',
            B: 'Participating in a religious ceremony',
            C: 'Practicing daily yoga',
            D: 'Engaging in nature-based spirituality',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER14',
        statement: 'Revisiting childhood memories brings comfort and joy.',
        associatedValue: 'Childhood Memories',
        followUp: {
          prompt: 'What childhood memory do you treasure?',
          options: {
            A: 'Playing with friends in the park',
            B: 'Family vacations',
            C: 'Celebrating holidays with loved ones',
            D: 'Participating in a favorite childhood activity',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER15',
        statement: 'Exploring philosophy, art, or science that challenges me broadens my horizons.',
        associatedValue: 'Exploring New Ideas',
        followUp: {
          prompt: 'Which new idea or concept has most recently expanded your worldview?',
          options: {
            A: 'Reading philosophical literature',
            B: 'Engaging with contemporary art',
            C: 'Studying scientific theories',
            D: 'Participating in intellectual discussions',
            E: 'Other',
          },
        },
      },
      {
        id: 'ER16',
        statement: 'Reflecting on past achievements or happy moments gives me perspective and satisfaction.',
        associatedValue: 'Positive Reflection',
        followUp: {
          prompt: 'Which past achievement or happy moment do you often revisit?',
          options: {
            A: 'Completing a significant project',
            B: 'Celebrating a personal milestone',
            C: 'Recalling a joyful family event',
            D: 'Reflecting on overcoming a major challenge',
            E: 'Other',
          },
        },
      },
    ],
  },
};

/*******************************************
 * MERGE quizData INTO A SINGLE QUESTION STACK
 *******************************************/
const questionStack = [];
// Each element: { id, type: 'scenario'/'rating', category: 'attitudinal'/..., prompt, options, ... }

const categories = ['attitudinal', 'creative', 'experiential'];

// Build questionStack in category order
categories.forEach((categoryName) => {
  const cat = quizData[categoryName];
  // Scenario-based questions
  cat.scenarioBased.forEach((q) => {
    questionStack.push({
      id: q.id,
      type: 'scenario',
      category: categoryName,
      prompt: q.prompt,
      options: q.options,
    });
  });
  // Rating-scale questions
  cat.ratingScale.forEach((q) => {
    questionStack.push({
      id: q.id,
      type: 'rating',
      category: categoryName,
      statement: q.statement,
      associatedValue: q.associatedValue,
      followUp: q.followUp,
    });
  });
});

const totalQuestions = questionStack.length;
// userAnswers array to store responses
let userAnswers = Array.from({ length: totalQuestions }).fill(null);
// current question index tracker
let currentQuestionIndex = 0;

/*******************************************
 * INTRO SCREEN & EVENT LISTENERS
 *******************************************/
const introScreen = document.getElementById('intro-screen');
const agreeCheckbox = document.getElementById('agree-checkbox');
const startQuizBtn = document.getElementById('start-quiz');
const quizContainer = document.getElementById('quiz-container');
const questionArea = document.getElementById('question-area');
const backBtn = document.getElementById('back-btn');
const progressFill = document.getElementById('progress-fill');
const progressText = document.getElementById('progress-text');
const analyzingScreen = document.getElementById('analyzing-screen');
const resultsContainer = document.getElementById('results-container');
const resultsContent = document.getElementById('results-content');
const retakeQuizBtn = document.getElementById('retake-quiz');

// Enable start button when agreement checkbox is checked
agreeCheckbox.addEventListener('change', () => {
  startQuizBtn.disabled = !agreeCheckbox.checked;
});

startQuizBtn.addEventListener('click', () => {
  fadeOut(introScreen, () => {
    introScreen.classList.add('hidden');
    quizContainer.classList.remove('hidden');
    fadeIn(quizContainer);
    updateProgressBar();
    renderQuestion();
  });
});

// Back button: navigate to previous question if available
backBtn.addEventListener('click', () => {
  if (currentQuestionIndex > 0) {
    currentQuestionIndex--;
    renderQuestion();
  }
});

/*******************************************
 * PROGRESS BAR UPDATE FUNCTION
 *******************************************/
function updateProgressBar() {
  const answered = userAnswers.filter(a => a !== null).length;
  const percent = Math.min((answered / totalQuestions) * 100, 100);
  progressFill.style.width = `${percent}%`;
  progressText.textContent = `${Math.round(percent)}% Completed`;
}

/*******************************************
 * QUESTION RENDERING FUNCTION
 *******************************************/
function renderQuestion() {
  fadeOut(questionArea, () => {
    questionArea.innerHTML = '';
    const qObj = questionStack[currentQuestionIndex];
    backBtn.disabled = (currentQuestionIndex === 0);

    if (qObj.type === 'scenario') {
      renderScenarioQuestion(qObj);
    } else {
      renderRatingQuestion(qObj);
    }
    fadeIn(questionArea);
  });
}

/*******************************************
 * RENDER SCENARIO QUESTION
 *******************************************/
function renderScenarioQuestion(q) {
  const container = document.createElement('div');
  container.classList.add('question');

  const promptP = document.createElement('p');
  promptP.textContent = q.prompt;
  container.appendChild(promptP);

  const ul = document.createElement('ul');
  ul.classList.add('options');

  const storedAnswer = userAnswers[currentQuestionIndex]?.scenarioChoice || null;

  for (const [key, val] of Object.entries(q.options)) {
    const li = document.createElement('li');
    const label = document.createElement('label');
    label.innerHTML = `
      <input type="radio" name="scenarioOption" value="${key}">
      ${key}. ${val.text}
    `;
    li.appendChild(label);
    ul.appendChild(li);
  }
  container.appendChild(ul);

  const nextBtn = document.createElement('button');
  nextBtn.classList.add('quiz-btn');
  const isLastQuestion = (currentQuestionIndex === totalQuestions - 1);
  nextBtn.textContent = isLastQuestion ? 'Submit' : 'Next';
  nextBtn.disabled = true;

  ul.addEventListener('change', () => {
    document.querySelectorAll('.options li').forEach(li => li.classList.remove('option-selected'));
    const selLi = document.querySelector('input[name="scenarioOption"]:checked')?.parentElement?.parentElement;
    if (selLi) {
      selLi.classList.add('option-selected');
    }
    nextBtn.disabled = false;
  });

  // Restore previous selection if exists
  if (storedAnswer) {
    const radioEl = ul.querySelector(`input[value="${storedAnswer}"]`);
    if (radioEl) {
      radioEl.checked = true;
      radioEl.parentElement.parentElement.classList.add('option-selected');
      nextBtn.disabled = false;
    }
  }

  nextBtn.addEventListener('click', () => {
    nextBtn.disabled = true;
    const selVal = container.querySelector('input[name="scenarioOption"]:checked')?.value;
    userAnswers[currentQuestionIndex] = { scenarioChoice: selVal };
    if (isLastQuestion) {
      showAnalyzingScreen();
    } else {
      currentQuestionIndex++;
      updateProgressBar();
      renderQuestion();
    }
  });

  // container.appendChild(nextBtn);
  // const footer = document.getElementById('quiz-footer');
  // footer.appendChild(nextBtn);
  // 💡 Inject next button into footer
  const footer = document.getElementById('quiz-footer');
  footer.querySelectorAll('#next-btn').forEach(btn => btn.remove());
  nextBtn.className = 'quiz-btn';
  nextBtn.id = 'next-btn';
  footer.appendChild(nextBtn);

  questionArea.appendChild(container);
}

/*******************************************
 * RENDER RATING QUESTION
 *******************************************/
function renderRatingQuestion(q) {
  const container = document.createElement('div');
  container.classList.add('question');

  const statementP = document.createElement('p');
  statementP.textContent = q.statement;
  container.appendChild(statementP);

  const ul = document.createElement('ul');
  ul.classList.add('rating-options');

  const storedAnswer = userAnswers[currentQuestionIndex] || {};

  // Create rating options 1 to 5
  for (let i = 1; i <= 5; i++) {
    const li = document.createElement('li');
    const label = document.createElement('label');
    label.innerHTML = `
      <input type="radio" name="rating" value="${i}">
      ${i}
    `;
    li.appendChild(label);
    ul.appendChild(li);
  }
  container.appendChild(ul);

  let followUpElement = null;
  let followUpDisplayed = false;

  const nextBtn = document.createElement('button');
  nextBtn.classList.add('quiz-btn');
  const isLastQuestion = (currentQuestionIndex === totalQuestions - 1);
  nextBtn.textContent = isLastQuestion ? 'Submit' : 'Next';
  nextBtn.disabled = true;

  ul.addEventListener('change', () => {
    document.querySelectorAll('.rating-options li').forEach(li => li.classList.remove('option-selected'));
    const selLi = ul.querySelector('input[name="rating"]:checked')?.parentElement?.parentElement;
    if (selLi) {
      selLi.classList.add('option-selected');
    }

    const selectedRating = Number.parseInt(ul.querySelector('input[name="rating"]:checked')?.value);
    if (selectedRating >= 4 && !followUpDisplayed) {
      followUpElement = createFollowUp(q, storedAnswer);
      container.appendChild(followUpElement);
      followUpDisplayed = true;
    } else if (selectedRating < 4 && followUpDisplayed) {
      if (followUpElement) {
        followUpElement.remove();
        followUpElement = null;
      }
      followUpDisplayed = false;
    }
    checkIfNextEnabled();
  });

  function checkIfNextEnabled() {
    const ratingVal = Number.parseInt(container.querySelector('input[name="rating"]:checked')?.value || '0');
    if (ratingVal >= 4 && followUpDisplayed) {
      const fSel = container.querySelector('input[name="followUp"]:checked');
      if (!fSel) {
        nextBtn.disabled = true;
        return;
      }
      if (fSel.value === 'Other') {
        const otherText = container.querySelector('#other-text')?.value.trim() || '';
        nextBtn.disabled = (otherText.length === 0);
      } else {
        nextBtn.disabled = false;
      }
    } else if (ratingVal > 0) {
      nextBtn.disabled = false;
    } else {
      nextBtn.disabled = true;
    }
  }

  if (storedAnswer.rating) {
    const ratingEl = ul.querySelector(`input[value="${storedAnswer.rating}"]`);
    if (ratingEl) {
      ratingEl.checked = true;
      ratingEl.parentElement.parentElement.classList.add('option-selected');
      if (storedAnswer.rating >= 4) {
        followUpElement = createFollowUp(q, storedAnswer);
        container.appendChild(followUpElement);
        followUpDisplayed = true;
      }
    }
  }

  nextBtn.addEventListener('click', () => {
    nextBtn.disabled = true;
    const selectedRating = Number.parseInt(container.querySelector('input[name="rating"]:checked')?.value);
    let followUpChoice = null;
    let otherText = null;
    if (selectedRating >= 4) {
      followUpChoice = container.querySelector('input[name="followUp"]:checked')?.value;
      if (followUpChoice === 'Other') {
        otherText = container.querySelector('#other-text')?.value.trim() || null;
      }
    }
    userAnswers[currentQuestionIndex] = {
      rating: selectedRating,
      followUpChoice,
      otherText,
    };
    if (isLastQuestion) {
      showAnalyzingScreen();
    } else {
      currentQuestionIndex++;
      updateProgressBar();
      renderQuestion();
    }
  });

  // container.appendChild(nextBtn);
  // const footer = document.getElementById('quiz-footer');
  // footer.appendChild(nextBtn);

  // 💡 Inject next button into footer
  const footer = document.getElementById('quiz-footer');
  footer.querySelectorAll('#next-btn').forEach(btn => btn.remove());
  nextBtn.className = 'quiz-btn';
  nextBtn.id = 'next-btn';
  footer.appendChild(nextBtn);

  questionArea.appendChild(container);
  checkIfNextEnabled();
}

/*******************************************
 * CREATE FOLLOW-UP ELEMENT
 *******************************************/
function createFollowUp(q, storedAnswer) {
  const followUpDiv = document.createElement('div');
  followUpDiv.id = 'follow-up';
  const p = document.createElement('p');
  p.innerHTML = `<strong>${q.followUp.prompt}</strong>`;
  followUpDiv.appendChild(p);

  const ul = document.createElement('ul');
  ul.classList.add('follow-up-options');
  for (const [key, val] of Object.entries(q.followUp.options)) {
    const li = document.createElement('li');
    const label = document.createElement('label');
    label.innerHTML = `
      <input type="radio" name="followUp" value="${val}">
      ${key}. ${val}
    `;
    li.appendChild(label);
    ul.appendChild(li);
  }
  followUpDiv.appendChild(ul);

  let otherInput = null;
  ul.addEventListener('change', () => {
    const selectedOpt = ul.querySelector('input[name="followUp"]:checked')?.value;
    if (selectedOpt === 'Other') {
      if (!otherInput) {
        otherInput = document.createElement('div');
        otherInput.id = 'other-input';
        otherInput.innerHTML = `<input type="text" id="other-text" placeholder="Please specify">`;
        followUpDiv.appendChild(otherInput);
        otherInput.querySelector('#other-text').addEventListener('input', () => {
          const nextBtn = document.querySelector('.quiz-btn');
          const textVal = otherInput.querySelector('#other-text').value.trim();
          nextBtn.disabled = (!textVal);
        });
      }
    } else {
      if (otherInput) {
        otherInput.remove();
        otherInput = null;
      }
    }
    const nextBtn = document.querySelector('.quiz-btn');
    if (selectedOpt && selectedOpt !== 'Other') {
      nextBtn.disabled = false;
    }
  });

  if (storedAnswer.followUpChoice) {
    const radio = ul.querySelector(`input[value="${storedAnswer.followUpChoice}"]`);
    if (radio) {
      radio.checked = true;
      if (storedAnswer.followUpChoice === 'Other') {
        if (!otherInput) {
          otherInput = document.createElement('div');
          otherInput.id = 'other-input';
          otherInput.innerHTML = `<input type="text" id="other-text" placeholder="Please specify">`;
          followUpDiv.appendChild(otherInput);
        }
        const textEl = otherInput.querySelector('#other-text');
        if (storedAnswer.otherText) {
          textEl.value = storedAnswer.otherText;
        }
      }
    }
  }
  return followUpDiv;
}

/*******************************************
 * SHOW ANALYZING SCREEN & CALCULATE RESULTS
 *******************************************/
function showAnalyzingScreen() {
  fadeOut(quizContainer, () => {
    quizContainer.classList.add('hidden');
    analyzingScreen.classList.remove('hidden');
    fadeIn(analyzingScreen);
  });
  setTimeout(() => {
    fadeOut(analyzingScreen, () => {
      analyzingScreen.classList.add('hidden');
      calculateAndShowResults();
    });
  }, 10000);
}

function calculateAndShowResults() {
  resultsContainer.classList.remove('hidden');
  fadeIn(resultsContainer);

  const scores = {};
  const allValues = [
    // Attitudinal
    'Acceptance of Suffering',
    'Courage in Adversity',
    'Optimism in Difficult Situations',
    'Forgiveness',
    'Patience',
    'Gratitude for Life',
    'Resilience',
    'Humility',
    'Faith or Spirituality',
    'Compassion for Others',
    'Responsibility and Duty',
    'Calmness and Presence',
    'Hope in the Future',
    'Detachment from Ego',
    'Generosity of Spirit',
    'Curiosity and Open-Mindedness',
    // Creative
    'Artistic Expression',
    'Professional Achievement',
    'Innovation and Problem-Solving',
    'Scientific Inquiry',
    'Building or Creating',
    'Mentorship and Teaching',
    'Problem-Solving in Communities',
    'Social Contribution',
    'Leadership in a Purposeful Cause',
    'Creative Hobbies',
    'Raising Children',
    'Developing Meaningful Projects',
    'Problem-Solving for Global Issues',
    'Cultural Creation',
    'Craftsmanship',
    'Writing and Storytelling',
    // Experiential
    'Deep Relationships',
    'Connection with Nature',
    'Aesthetic Appreciation',
    'Cultural Immersion',
    'Sensory Experiences',
    'Enjoyment of Simple Moments',
    'Adventure and Exploration',
    'Intellectual Discovery',
    'Shared Experiences',
    'Appreciation of Nature’s Cycles',
    'Emotional Connection',
    'Moments of Awe',
    'Spiritual Experiences',
    'Childhood Memories',
    'Exploring New Ideas',
    'Positive Reflection',
  ];
  allValues.forEach(val => scores[val] = 0);

  questionStack.forEach((qObj, idx) => {
    const ans = userAnswers[idx];
    if (!ans) {
      return;
    }
    if (qObj.type === 'scenario' && ans.scenarioChoice) {
      const chosen = qObj.options[ans.scenarioChoice];
      if (chosen && chosen.values) {
        chosen.values.forEach((v) => {
          scores[v] += 2;
        });
      }
    } else if (qObj.type === 'rating' && ans.rating) {
      if (ans.rating >= 4) {
        scores[qObj.associatedValue] += (ans.rating - 1);
      }
    }
  });

  const categoryScores = {
    attitudinal: {},
    creative: {},
    experiential: {},
  };

  for (const val of allValues) {
    const cat = findCategoryByValue(val);
    if (cat) {
      categoryScores[cat][val] = scores[val];
    }
  }

  function findCategoryByValue(value) {
    const aVals = questionStack.filter(q => q.category === 'attitudinal').flatMap(q => q.type === 'scenario'
      ? Object.values(q.options).flatMap(opt => opt.values)
      : [q.associatedValue]);
    const cVals = questionStack.filter(q => q.category === 'creative').flatMap(q => q.type === 'scenario'
      ? Object.values(q.options).flatMap(opt => opt.values)
      : [q.associatedValue]);
    const eVals = questionStack.filter(q => q.category === 'experiential').flatMap(q => q.type === 'scenario'
      ? Object.values(q.options).flatMap(opt => opt.values)
      : [q.associatedValue]);
    if (aVals.includes(value)) {
      return 'attitudinal';
    }
    if (cVals.includes(value)) {
      return 'creative';
    }
    if (eVals.includes(value)) {
      return 'experiential';
    }
    return null;
  }

  resultsContent.innerHTML = '';

  function getTop3(obj) {
    return Object.entries(obj)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .filter(x => x[1] > 0);
  }

  ['attitudinal', 'creative', 'experiential'].forEach((cat) => {
    const top3 = getTop3(categoryScores[cat]);
    const catDiv = document.createElement('div');
    catDiv.classList.add('results-category');
    const catTitle = `${cat} values`;
    // const catTitle = `${cat.charAt(0).toUpperCase() + cat.slice(1)} Values`;
    const h3 = document.createElement('h3');
    h3.textContent = catTitle;
    catDiv.appendChild(h3);

    if (top3.length === 0) {
      const p = document.createElement('p');
      p.textContent = 'No values identified in this category.';
      catDiv.appendChild(p);
    } else {
      const ul = document.createElement('ul');
      top3.forEach(([valName]) => {
        const li = document.createElement('li');
        const lowercaseValue = valName
          .split(' ')
          .map(word => word.charAt(0).toLowerCase() + word.slice(1))
          .join(' ');
        li.textContent = lowercaseValue;
        // li.textContent = valName;
        ul.appendChild(li);
      });
      catDiv.appendChild(ul);
    }
    resultsContent.appendChild(catDiv);
  });

  // const allQASection = document.createElement('div');
  // allQASection.classList.add('all-qa');

  // const qaHeader = document.createElement('h3');
  // qaHeader.textContent = "Your Full Answers";
  // allQASection.appendChild(qaHeader);

  // questionStack.forEach((q, i) => {
  //   const a = userAnswers[i];
  //   if (!a) return;

  //   const item = document.createElement('div');
  //   item.classList.add('qa-item');

  //   const qText = document.createElement('p');
  //   qText.innerHTML = `<strong>Q${i + 1}:</strong> ${q.type === 'scenario' ? q.prompt : q.statement}`;
  //   item.appendChild(qText);

  //   const aText = document.createElement('p');
  //   if (q.type === 'scenario') {
  //     const chosen = q.options[a.scenarioChoice];
  //     aText.innerHTML = `<strong>Your Answer:</strong> ${a.scenarioChoice}. ${chosen?.text || 'N/A'}`;
  //   } else {
  //     aText.innerHTML = `<strong>Rating:</strong> ${a.rating}`;
  //     if (a.rating >= 4 && a.followUpChoice) {
  //       aText.innerHTML += `<br/><strong>Follow-Up:</strong> ${a.followUpChoice}`;
  //       if (a.followUpChoice === 'Other' && a.otherText) {
  //         aText.innerHTML += ` — ${a.otherText}`;
  //       }
  //     }
  //   }
  //   item.appendChild(aText);

  //   allQASection.appendChild(item);
  // });

  // resultsContent.appendChild(allQASection);

  // const answersWithQuestions = questionStack.map((q, i) => {
  //   const ans = userAnswers[i];
  //   return {
  //     index: i + 1,
  //     id: q.id,
  //     type: q.type,
  //     category: q.category,
  //     question: q.type === 'scenario' ? q.prompt : q.statement,
  //     answer: q.type === 'scenario'
  //       ? (ans?.scenarioChoice ? `${ans.scenarioChoice}. ${q.options[ans.scenarioChoice]?.text || 'N/A'}` : null)
  //       : {
  //           rating: ans?.rating,
  //           followUpChoice: ans?.followUpChoice || null,
  //           otherText: ans?.otherText || null,
  //         },
  //   };
  // });

  const topValuesByCategory = {};
  ['attitudinal', 'creative', 'experiential'].forEach((cat) => {
    topValuesByCategory[cat] = getTop3(categoryScores[cat]);
  });

  // ✅ Dispatch a custom event with all answers
  // document.dispatchEvent(new CustomEvent('quiz:complete', {
  //   detail: answersWithQuestions,
  // }));

  document.dispatchEvent(new CustomEvent('quiz:complete', {
    detail: topValuesByCategory,
  }));
}

/*******************************************
 * RETAKE QUIZ
 *******************************************/
retakeQuizBtn.addEventListener('click', () => {
  fadeOut(resultsContainer, () => {
    resultsContainer.classList.add('hidden');
    // Reset quiz data
    userAnswers = Array.from({ length: totalQuestions }).fill(null);
    currentQuestionIndex = 0;
    progressFill.style.width = '0%';
    progressText.textContent = '0% Completed';
    quizContainer.classList.remove('hidden');
    fadeIn(quizContainer);
    updateProgressBar();
    renderQuestion();
  });
});

/*******************************************
 * FADE HELPERS
 *******************************************/
function fadeOut(element, callback) {
  element.classList.add('fade-out');
  setTimeout(() => {
    element.classList.remove('fade-out');
    if (callback) {
      callback();
    }
  }, 800);
}

function fadeIn(element) {
  element.classList.remove('fade-out');
  element.style.opacity = '0';
  setTimeout(() => {
    element.style.opacity = '1';
  }, 10);
}
