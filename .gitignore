# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/vitest-test-results

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# Sentry Config File
.env.sentry-build-plugin

# local folder
local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Database
*.db

# storybook
storybook-static
*storybook.log
build-archive.log

# playwright
/test-results/
/playwright-report/
/playwright/.cache/
