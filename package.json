{"name": "next-js-boilerplate", "author": "Ixartz (https://github.com/ixartz)", "engines": {"node": ">=20"}, "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev --turbopack", "dev": "run-p db-server:file dev:*", "build:next": "next build", "build": "run-p db-server:memory build:* --race", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "commit": "commit", "test": "vitest run --browser.headless", "test:e2e": "playwright test", "db-server:file": "pglite-server --db=local.db", "db-server:memory": "pglite-server", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "postbuild": "next-sitemap"}, "dependencies": {"@arcjet/next": "^1.0.0-beta.8", "@clerk/localizations": "^3.16.5", "@clerk/nextjs": "^6.22.0", "@electric-sql/pglite": "^0.3.3", "@hookform/resolvers": "^5.1.1", "@logtail/pino": "^0.5.5", "@react-oauth/google": "^0.12.2", "@rive-app/react-canvas": "^4.21.4", "@sentry/nextjs": "^9.29.0", "@t3-oss/env-nextjs": "^0.13.8", "antd": "^5.26.1", "drizzle-orm": "^0.44.2", "js-cookie": "^3.0.5", "next": "^15.3.3", "next-intl": "^4.3.3", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "posthog-js": "^1.252.1", "react": "19.1.0", "react-dom": "19.1.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.58.0", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "zod": "^3.25.64"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@chromatic-com/playwright": "^0.12.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@electric-sql/pglite-socket": "^0.0.8", "@eslint-react/eslint-plugin": "^1.52.2", "@faker-js/faker": "^9.8.0", "@next/bundle-analyzer": "^15.3.3", "@next/eslint-plugin-next": "^15.3.3", "@playwright/test": "^1.53.0", "@spotlightjs/spotlight": "^2.13.3", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/test-runner": "^0.23.0", "@tailwindcss/postcss": "^4.1.10", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.1", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.5.2", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "@vitest/expect": "^3.2.3", "checkly": "^5.6.0", "chromatic": "^12.2.0", "conventional-changelog-conventionalcommits": "^9.0.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "http-server": "^14.1.1", "lefthook": "^1.11.13", "next-sitemap": "^4.2.3", "npm-run-all": "^4.1.5", "postcss": "^8.5.5", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.5", "start-server-and-test": "^2.0.12", "storybook": "^8.6.14", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.3", "vitest-browser-react": "^0.2.0"}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/github"]}}