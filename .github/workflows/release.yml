name: Release

on:
  workflow_run:
    workflows: [CI]
    types:
      - completed
    branches:
      - main

jobs:
  release:
    strategy:
      matrix:
        node-version: [22.x]

    name: Create a new release
    runs-on: ubuntu-latest

    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js environment
        uses: ./.github/actions/setup-project
        with:
          node-version: ${{ matrix.node-version }}

      - name: Verify the integrity of provenance attestations and registry signatures for installed dependencies
        run: npm audit signatures

      - name: Release
        run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
