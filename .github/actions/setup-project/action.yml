name: Setup Node.js and dependencies
description: Setup Node.js environment with npm dependencies

inputs:
  node-version:
    description: Node.js version
    required: true

runs:
  using: composite
  steps:
    - name: Use Node.js ${{ inputs.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: npm

    - name: Restore or cache node_modules
      id: cache-node-modules
      uses: actions/cache@v4
      with:
        path: node_modules
        key: node-modules-${{ inputs.node-version }}-${{ hashFiles('package-lock.json') }}

    - name: Install dependencies
      if: steps.cache-node-modules.outputs.cache-hit != 'true'
      shell: bash
      run: npm ci
