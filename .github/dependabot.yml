version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: npm
    # Look for `package.json` and `lock` files in the root directory
    directory: /
    # Check the npm registry for updates every month
    schedule:
      interval: monthly
      time: '06:00'
    # Limit the number of open pull requests for version updates to 1
    open-pull-requests-limit: 1
    # Commit message format
    commit-message:
      # Prefix all commit messages and pull request titles with "chore"
      prefix: chore
    # Group updates into a single pull request
    groups:
      # The name of the group (identifier)
      npm-deps:
        update-types: [minor, patch]
    # Only allow minor and patch updates
    ignore:
      - dependency-name: '*'
        update-types: ['version-update:semver-major']

  # Enable version updates for GitHub Actions
  - package-ecosystem: github-actions
    # Look for `.github/workflows` in the root directory
    directory: /
    # Check GitHub Actions for updates every month
    schedule:
      interval: monthly
      time: '06:05'
    # Limit the number of open pull requests for version updates to 1
    open-pull-requests-limit: 1
    # Commit message format
    commit-message:
      # Prefix all commit messages and pull request titles with "chore"
      prefix: chore
