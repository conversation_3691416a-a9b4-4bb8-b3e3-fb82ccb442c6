image: node:22.14.0
pipelines:
  pull-requests:
    '**':
      - step:
          name: Abhi Code Review
          script:
            - pipe: atlassian/bitbucket-chatgpt-codereview:0.2.0
              variables:
                OPENAI_API_KEY: $OPENAI_API_KEY # Ensure the API Key is set in Bitbucket secrets
                BITBUCKET_ACCESS_TOKEN: $BITBUCKET_ACCESS_TOKEN # Ensure the Bitbucket Access Token is set
                MODEL: $MODEL # Use GPT-4 for comprehensive review
                ORGANIZATION: '' # Optional, use if you have an OpenAI organization context
                MESSAGE: |
                  Please review the following changes in the pull request. The objective is to ensure that the code adheres to best practices, is optimized for performance, and follows common coding standards.
                  - Look for areas where the code could be simplified or refactored for better readability.
                  - Suggest any potential improvements to performance and security.
                  - Check for any obvious bugs or issues that could lead to runtime errors.
                  - Recommend changes to improve maintainability and scalability, especially in the context of the overall project architecture.
                FILES_TO_REVIEW: '' # Leave empty to review all files changed in the PR
                DEBUG: 'true' # Set to true for debugging information
                CHATGPT_PROMPT_MAX_TOKENS: $CHATGPT_MAX_TOKENS
