// import type { NextRequest } from 'next/server';
// // middleware.ts
// import { NextResponse } from 'next/server';

// export function middleware(request: NextRequest) {
//   const { pathname } = request.nextUrl;

//   // Redirect root (/) to /dogo
//   if (pathname === '/') {
//     const url = request.nextUrl.clone();
//     url.pathname = '/hero';
//     return NextResponse.redirect(url);
//   }

//   return NextResponse.next();
// }

// export const config = {
//   matcher: ['/'], // only run for root
// };
// middleware.ts
import createMiddleware from 'next-intl/middleware';
import { AppConfig } from './utils/AppConfig';

export default createMiddleware({
  locales: AppConfig.locales,
  defaultLocale: AppConfig.defaultLocale,
  localePrefix: AppConfig.localePrefix,
});

export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)'],
};
