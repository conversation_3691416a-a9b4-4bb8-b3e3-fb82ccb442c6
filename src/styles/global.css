@import 'tailwindcss';

/* Root Variables */
:root {
  --primary-color: #e16758;
  --text-color: #454545;
  --text-muted-color: #777980;
  --highlight-color: #103962;
  --bg-overlay: #ffffff78;
  --border-color: #e8e8e8;
  --border-light: #d2d3d6;
  --text-light: #4545458f;
  --desc-color: #454545cc;
  --footer-color: #82858d;
  --footer-bg: #f6fcff;
  --header-border: #86868730;
  --option-bg: #e5f6fd;
  --footer-border: #def5ff;
  --success-color: #34a610;
  --tab-border-color: #d3dce6;
  --tab-active-color: #f26a4b;
  --faq-shadow-color: #1459711a;
  --white: #ffffff;
}

/* Webfont: DojowellBasic */
@font-face {
  font-family: 'DojowellBasic';
  src: url('/fonts/dojowell basic.eot');
  /* IE9 Compat Modes */
  src:
    url('/fonts/dojowell basic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/fonts/dojowell basic.woff') format('woff'),
    /* Modern Browsers */ url('/fonts/dojowell basic.woff2') format('woff2'),
    /* Modern Browsers */ url('/fonts/dojowell basic.ttf') format('truetype');
  /* Safari, Android, iOS */
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}

/* @font-face {
  font-family: 'Dojowell Basic';
  src: url('/fonts/dojowell-basic.woff2') format('woff2'),
    url('/fonts/dojowell-basic.woff') format('woff'),
    url('/fonts/dojowell-basic.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
} */

/* Scrollbar Hidden */
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

body {
  font-family: 'DojowellBasic', Georgia;
}

body::-webkit-scrollbar {
  display: none;
}

.d-flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mt-1 {
  margin-top: 5px;
}

.mt-2 {
  margin-top: 10px;
}

.mt-3 {
  margin-top: 15px;
}

.mt-5 {
  margin-top: 50px;
}

.mt-10 {
  margin-top: 100px;
}

/* Layout */
.main-container {
  /* height: 100vh; */
  width: 100vw;
  background-image: url('../../public/assets/images/Section 1 — Hero.png');
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: auto;
}

/* Header */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: space-around;
  /* background-color: #f6fcff; */
  /* backdrop-filter: blur(8px); */
}

.header.scrolled {
  background-color: #f6fcff;
  backdrop-filter: none;
}

.header-logo {
  display: flex;
  align-items: center;
}

.logo {
  padding: 0;
  width: 196px;
  height: 52px;
}

.btn-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quiz-button {
  width: 176px;
  height: 50px;
  /* margin-top: 25px; */
  border-radius: 10px;
  border-width: 1px;
  border: 1px solid var(--header-border);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color);
  font-size: 20px;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.language-select {
  width: 126px;
  height: 50px;
  /* margin-top: 25px; */
  border-radius: 10px;
  border-width: 1px;
  border: 1px solid var(--header-border);
  display: flex;
  justify-content: start;
  align-items: center;
  appearance: none;
  color: var(--text-color);
  padding: 10px;
  /* padding-left: 15px; */
  font-size: 20px;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  background-color: transparent;
  background-image: url('/assets/images/Arrow - Left 2.svg');
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 12px;
}

.dropdown-select {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  border-width: 1px;
  border: none;
  display: flex;
  justify-content: start;
  align-items: center;
  appearance: none;
  color: var(--text-color);
  padding: 10px;
  /* padding-left: 15px; */
  background-color: transparent;
  background-image: url('/assets/images/Arrow - Left 2.svg');
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 12px;
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 400;
  font-size: 20px;
}

.dropdown-select:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

.placeholder-colour {
  color: #82858d;
}

option {
  background-color: var(--option-bg);
}

/* Footer */
.footer {
  position: sticky;
  bottom: 0;
  z-index: 1000;
  width: 100%;
  min-height: 70px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: var(--footer-color);
  /* background-color: transparent; */
  background-color: var(--footer-bg);
  border-top: 1px solid var(--footer-border);
  /* font-family: Optima Medium; */
  font-family: 'DojowellBasic', Georgia;
}

.footer-tabs {
  display: flex;
  gap: 10px;
}

.footer-text {
  margin-right: 5px;
}

/* Buttons */
.btndiv,
.submitbtn {
  width: 350px;
  height: 80px;
  border-radius: 50px;
  font-size: 25px;
  border: 1px solid var(--primary-color);
  padding: 15px 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14px;
  text-align: center;
  margin: 0 auto;
}

.btndiv:hover,
.submitbtn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

.btndivabc {
  font-size: 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  padding-top: 20px;
}

.btndivthankyou {
  width: 45%;
  height: 60px;
  border-radius: 50px;
  border: 1px solid var(--primary-color);
  padding: 15px 0;
  gap: 10px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 25px;
  line-height: 100%;
  color: var(--primary-color);
  background-color: var(--white);
  cursor: pointer;
}

/* Modal */
.ant-modal .ant-modal-content {
  padding: 50px 42px !important;
}

.ant-modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Input */
.inputfile {
  margin-bottom: 30px;
  width: 100%;
  padding-left: 25px;
  height: 70px;
  border-radius: 37px;
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 400;
  font-size: 20px;
  border: 1px solid var(--border-light);
}

.inputdropdown {
  margin-bottom: 30px;
  width: 100%;
  padding-left: 25px;
  height: 70px;
  border-radius: 37px;
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 400;
  font-size: 20px;
  border: 1px solid var(--border-light);
}

/* Typography */
.titletext {
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 30px;
  line-height: 130%;
  text-align: center;
  color: var(--text-color);
  margin-bottom: 30px;
}

.desc {
  font-size: 20px;
  font-weight: 400;
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  color: var(--text-muted-color);
  line-height: 30px;
  text-align: center;
  margin-bottom: 30px;
}

.psub {
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 21px;
  text-align: center;
  line-height: 30px;
  color: var(--text-muted-color);
}

.psubtext {
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 21px;
  text-align: center;
  font-weight: 700;
  color: var(--highlight-color);
  line-height: 30px;
}

.resend {
  font-weight: 550;
  font-size: 23px;
  line-height: 100%;
  margin-top: 30px;
  text-decoration: underline;
  text-decoration-style: solid;
  text-decoration-thickness: 0%;
  text-decoration-skip-ink: auto;
  color: var(--primary-color) !important;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.error-text {
  color: var(--primary-color);
  font-size: 14px;
  margin-top: 4px;
  font-weight: 500;
  width: 100%;
  padding-left: 10px;
}

.hero-title {
  /* font-family: Optima; */
  /* font-family: 'Dojowell Basic'; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 50px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: var(--text-color);
  z-index: 99;
}

/* Coming Soon Layout */
.coming-logo {
  margin-bottom: 20px;
}

.comingsoon-container {
  /* height: 100vh; */
  min-height: 919px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 40px;
  overflow: auto;
}

.comingsoon-title {
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 40px;
  text-align: center;
  color: var(--text-color);
  max-width: 1075px;
}

.comingsoon-desc {
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 550;
  font-size: 25px;
  text-align: center;
  color: var(--desc-color);
  max-width: 1296px;
}

/* Counter */
.counter {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 50px;
}

.count-card {
  width: 189px;
  height: 200px;
  border-radius: 35px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-overlay);
}

.count {
  font-size: 70px;
  font-weight: 700;
  color: var(--text-color);
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  line-height: 100%;
}

.count-label {
  font-size: 30px;
  font-weight: 550;
  color: var(--text-light);
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
}

.thanks-logo {
  display: flex;
  justify-content: center;
}

.thanksbtn {
  display: flex;
  justify-content: space-between;
}

/* Support and Faq Page  */
.main-long-container {
  height: 100%;
  width: 100vw;
  /* background-image: url('../../public/assets/images/bglong.png'); */
  background-size: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: auto;
}

.main-div {
  background-image: url('../../public/assets/images/bglong (1).png');
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  background-color: #eefdff;
}

.content-long-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.support-page {
  height: calc(100vh);
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.support-page-secondary {
  height: 800px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.support-title {
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 60px;
  text-align: center;
  color: var(--text-color);
  max-width: 1320px;
}

.join-desc {
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 550;
  font-size: 25px;
  letter-spacing: 0%;
  text-align: center;
  color: var(--desc-color);
  z-index: 999;
}

/* .join-desc:hover {
  color: var(--desc-color);
} */

.textarea {
  margin-bottom: 30px;
  width: 100%;
  padding: 25px;
  height: 70px;
  border-radius: 37px;
  /* font-family: Optima Medium; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 400;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0%;
  border: 1px solid var(--border-light);
  resize: none;
  height: 200px;
}

.confirm {
  font-size: 23px;
  font-weight: 700;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  color: var(--success-color);
  width: 100%;
  margin-left: 2px;
}

.w-50 {
  width: 49%;
}

.support-form {
  width: 950px;
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  gap: 19px;
}

.tab-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 8px 18px;
  border-radius: 999px;
  border: 1px solid var(--tab-border-color);
  background-color: var(--white);
  color: #8a9aad;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.tab-btn.active {
  border-color: var(--tab-active-color);
  color: var(--tab-active-color);
  font-weight: 600;
}

.faq-container {
  width: 100vw;
  height: 150vh;
  overflow: auto;
  display: flex;
  justify-content: start;
  align-items: center;
  flex-direction: column;
  padding: 20px 0;
}

.faq-page {
  height: 82vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.faq-accordian {
  width: 1296px;
  min-height: 73px;
  padding: 20px;
  box-shadow: 0 0 20px 0 var(--faq-shadow-color);
  margin-top: 20px;
  border-radius: 15px;
}

.questions {
  font-size: 22px;
  font-weight: 700;
  /* font-family: Optima bold; */
  font-family: 'DojowellBasic', Georgia;
  color: var(--text-color);
}

.answer-container {
  max-height: 0;
  overflow: hidden;
  transition:
    max-height 0.4s ease,
    padding 0.4s ease;
}

.answer-container.open {
  max-height: 500px;
  padding: 16px 0 0 0;
}

.answer {
  font-size: 20px;
  font-weight: 550;
  color: var(--desc-color);
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.answer-container.open .answer {
  opacity: 1;
}

.arrow-icon {
  transition: transform 0.3s ease;
  display: inline-block;
  font-size: 16px;
  color: var(--footer-color);
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

/* .exploring-container {
  background-image: url('../../public/assets/images/bg.png');
} */

.exploring-container {
  flex-grow: 1;
  overflow: hidden;
  /* Or controlled via JS */
}

/* .animation-container {
  width: 100vw;
  min-width: 1400px;
  height: 100vh;
  overflow: hidden;
} */
.animation-container {
  width: 100vw;
  max-width: 1700px;
  /* min-width: 1400px; */
  height: 100vh;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.animation-subscriptioncontainer {
  width: 100vw;
  max-width: 1700px;
  /* min-width: 1400px; */
  height: 100vh;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.hero-section {
  width: 100vw;
  height: 100vh;
  background-image: url('/assets/images/herobg.png');
  background-size: cover;
  /* stretches image to cover full area */
  background-repeat: no-repeat;
  background-position: center;
  /* keeps it centered */
}

.hero-content {
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: center;
  gap: 20px;
  height: calc(100vh - 80px);
  padding-bottom: 50px;
}

.bear-animation {
  width: 300px;
  height: 300px;
  overflow: hidden;
  position: absolute;
  top: 31%;
  right: calc(55%);
}

.bear-animation-explore {
  width: 450px;
  height: 450px;
  overflow: hidden;
  position: absolute;
  bottom: 13%;
  left: calc(21%);
  transition: left 1s ease-out;
}

.audio-modal {
  width: 416px;
  height: 467px;
  border-radius: 25px;
  box-shadow: 0 0 30px 0 #1459714d;
  position: absolute;
  top: 20%;
  right: 40%;
}

.audio-box {
  position: relative;
  top: 0;
  /* width: 376px; */
  height: 100%;
}

.audio-player {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  justify-content: center;
  padding: 25px 30px;
}

.progress-row {
  /* display: flex; */
  align-items: center;
  justify-content: space-between;
  font-family: sans-serif;
  color: #333;
}

.btn-div {
  text-align: center !important;
  margin-top: 1.5rem;
}

input[type='range'] {
  flex: 1;
  accent-color: #333;
  width: 350px;
  height: 5px;
  border-radius: 50px;
}

.cloud-text {
  background-image: url('/assets/images/cloud.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 32%;
  left: 45%;
  width: 500px;
  height: 250px;
  color: #2e5b7d;
  display: flex;
  justify-content: center;
  /* font-family: Optima; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 21px;
  line-height: 33px;
  text-align: center;
  align-items: center;
  padding: 20px;
  transition: opacity 1s ease-out;
  transition: left 1s ease-out;
  opacity: 0;
  pointer-events: none;
  animation: fadeInEaseOut 2s ease-out forwards;
  z-index: 1;
}

.controls {
  display: flex;
  width: 100%;
  padding: 0 15px;
  justify-content: space-between;
}

.carousal-dots {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-bottom: 40px;
}

.carousal-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #d8d6d6;
}

.carousal-dot-active {
  background-color: #e16758;
}

@keyframes fadeInEaseOut {
  0% {
    transform: translateY(10px);
    /* slight upward motion */
  }

  100% {
    transform: translateY(0);
  }
}

@media screen and (min-width: 500px) and (max-width: 1035px) {
  .hero-content {
    gap: 20px;
    /* height: calc(100vh - 30px); */
    padding-bottom: 10px;
  }

  .hero-title {
    font-size: 35px;
  }

  .join-desc {
    font-size: 20px;
  }
}

@media screen and (min-width: 200px) and (max-width: 700px) {
  .subscription-btn {
    width: 265px !important;
    height: 60px !important;
    font-size: 23px !important;
    margin-top: 8px !important;
  }
}

@media screen and (min-height: 500px) and (max-height: 800px) {
  .bear-animation {
    width: 300px;
    height: 300px;
    overflow: hidden;
    position: absolute;
    top: 28%;
    right: calc(55%);
  }

  .cloud-text {
    top: 20%;
  }

  .parent-count {
    margin-top: 25px !important;
  }

  .hero-title {
    font-size: 40px;
  }

  .count-title {
    font-size: 20px !important;
  }
}

@media screen and (min-width: 968px) and (max-width: 1024px) {
  .bear-animation-explore {
    width: 450px;
    height: 450px;
    bottom: 13%;
    left: 0% !important;
  }

  .bear-animation-explore > div {
    height: 100%;
    width: 80% !important;
  }

  .cloud-text {
    top: 24%;
    left: 10% !important;
    width: 500px;
    height: 250px;
  }

  .animation-container {
    width: 100vw;
    min-width: 1160px;
    height: 100vh;
    /* height: calc(100vh - 30px); */
    overflow: hidden;
  }

  .animation-subscriptioncontainer {
    width: 100vw;
    min-width: 1160px;
    height: 100vh;
    /* height: calc(100vh - 30px); */
    overflow: hidden;
  }

  .audio-modal {
    width: 320px;
    height: 385px;
    top: 8%;
    right: 31% !important;
  }

  .audio-player {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    padding: 10px 30px;
  }

  .progress-input {
    width: 260px !important;
  }

  .audio-div {
    height: 20% !important;
  }

  .music-btn {
    font-size: 23px;
    height: 70px;
  }

  .faq-container {
    padding: 0 20px 20px;
  }

  .tab-container {
    padding: 0 20px;
  }
}

@media (max-width: 500px) {
  .bear-subscription-animation {
    width: 250px !important;
    height: 250px !important;
    overflow: hidden;
    /* position: absolute; */
    /* top: 310px;
  right: calc(55%); */
  }

  .pricing-container {
    gap: 2rem !important;
  }
}

@media (max-width: 500px) {
  .features li {
    font-size: 17px !important;
  }
}

@media screen and (min-width: 500px) and (max-width: 968px) {
  .bear-animation-explore {
    width: 450px;
    height: 450px;
    bottom: 13%;
    left: 0% !important;
  }

  .bear-animation-explore > div {
    height: 100%;
    width: 65% !important;
  }

  .cloud-text {
    top: 27%;
    left: 2% !important;
    width: 500px;
    height: 250px;
  }

  .animation-container {
    width: 100vw;
    min-width: 890px;
    height: 100vh;
    object-fit: cover;
    /* height: calc(100vh - 40px); */
    overflow: hidden;
  }

  .animation-subscriptioncontainer {
    width: 100vw;
    min-width: 890px;
    height: 100vh;
    object-fit: cover;
    /* height: calc(100vh - 40px); */
    overflow: hidden;
  }

  .audio-modal {
    width: 320px;
    height: 385px;
    top: 4%;
    right: 28% !important;
  }

  .audio-player {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    padding: 10px 30px;
  }

  .audio-div {
    height: 20% !important;
  }

  .music-btn {
    font-size: 22px;
    height: 60px;
    margin-bottom: 0px !important;
  }

  .progress-input {
    width: 260px !important;
  }

  .support-form {
    width: 670px;
  }

  .faq-container {
    padding: 0 20px 20px;
  }

  .tab-container {
    padding: 0 20px;
  }
}

/* Responsive Media Queries */
@media screen and (min-width: 500px) and (max-width: 1024px) {
  .inputfile {
    width: 100%;
  }

  .inputdropdown {
    width: 100%;
  }

  .coming-logo {
    margin-top: 250px;
  }

  .comingsoon-container {
    padding: 0 20px;
    gap: 20px;
  }

  .btndiv {
    margin-bottom: 50px;
  }
}

@media screen and (min-width: 200px) and (max-width: 504px) {
  .counter {
    gap: 10px;
  }

  .count-card {
    width: 145px;
  }

  .logo {
    width: auto;
  }

  .content-long-container {
    padding: 0 20px;
  }

  .support-form,
  .inputfile,
  .submitbtn,
  .btndivthankyou {
    width: 100%;
  }

  .inputdropdown {
    width: 100%;
  }

  .btndiv {
    width: 100%;
    margin-bottom: 30px;
  }

  .coming-logo {
    margin-top: 100px;
  }

  .comingsoon-container {
    padding: 0 20px;
    gap: 20px;
  }

  .ant-modal .ant-modal-content {
    padding: 50px 20px;
  }

  .psubtext {
    margin-top: 30px;
  }

  .thanksbtn {
    flex-direction: column;
    gap: 10px;
  }

  .quiz-button {
    width: 140px;
    font-size: 16px;
  }

  .language-select {
    width: 105px;
    font-size: 16px;
  }

  .dropdown-select {
    font-size: 16px;
  }

  .tab-container {
    justify-content: center;
  }

  .faq-container {
    padding: 0 20px 20px;
  }

  .support-title {
    font-size: 52px;
  }
}

@media screen and (max-width: 710px) {
  .footer {
    padding: 12px 10px;
    flex-wrap: wrap;
  }

  .footer-tabs {
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
    margin-top: 10px;
  }
}

/* ================= subscription ===================== */

.main-subscription-container {
  height: 100vh;
  /* width: 100vw; */
  /* background-image: url('/assets/images/bg.png'); */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  /* display: flex; */
  flex-direction: column;
  justify-content: space-between;
  overflow: auto;
  font-family: 'Inter', sans-serif;
}

.pricing-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 50px 120px;
  flex-grow: 1;
  gap: 3rem;
}

.bear-section {
  order: 2;
}

.pricing-content-section {
  order: 1;
}

.pricing-content {
  max-width: 700px;
  color: #2d2d2d;
  /* width: 60%; */
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  /* Adjust depending on your design */
}

.no-subscriptions-message {
  text-align: center;
  font-size: 25px;
  color: #999;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.headline {
  font-size: 40px;
  font-weight: 700;
  line-height: 1.2;
  color: #222;
}

.cancel-subscription {
  font-size: 20px;
  color: #e16758;
  /* font-family: Optima normal; */
  font-family: 'DojowellBasic', Georgia;
  text-align: center;
  margin-top: 20px !important;
  width: fit-content;
  margin: 0 auto;
}

.cancel-subscription:hover {
  text-decoration: underline;
  cursor: pointer;
}

.subhead {
  font-size: 23px;
  margin: 16px 0 20px;
  line-height: 1.6;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  color: #666869;
}

.features {
  list-style: none;
  padding: 0;
  font-size: 18px;
  line-height: 1.6;
  text-align: left;
  margin-bottom: 15px;
}

.features li {
  margin-bottom: 16px;
  line-height: 1.6;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  color: #82858d;
  font-size: 19px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.features-span {
  margin-bottom: 12px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #82858d;
  font-size: 19px;
  margin-right: 4px;
}

.icon {
  width: 23px;
  flex-shrink: 0;
  /* margin-top: 2px; */
}

.icons {
  width: 25px;
  height: 25px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-icon {
  height: 20px;
  width: 20px;
}

.emoji {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.pricing-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  margin-bottom: 35px;
  margin-top: 25px;
  flex-wrap: wrap;
  /* height: 250px; */
}

.plan {
  /* border: 1px solid #ddd; */
  border-radius: 20px;
  background: white;
  text-align: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  padding: 17px 25px;
  width: 215px;
  /* height: 180px; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* transition: all 0.3s ease; */
  cursor: pointer;
  border: 2px solid transparent;
  transition:
    border 0.2s ease,
    box-shadow 0.2s ease;
  outline: none;
}

/* Selected card */
.plan.selected {
  border: 2px solid #e16758;
  width: 215px;
  /* height: 220px; */
  padding: 17px 25px;
  /* box-shadow: 0 8px 20px rgba(224, 81, 69, 0.2); */
  z-index: 1;
}

/* Badge */
.badge {
  font-size: 20px;
  font-weight: 600;
  color: #e16758;
  margin-bottom: 2px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
}

.plan:focus {
  outline: none;
  box-shadow: none;
}

.plan:focus-visible {
  outline: 2px solid #e16758;
}

.label {
  font-size: 25px;
  color: #4545458f;
  font-weight: 500;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  /* margin-bottom: 6px; */
}

.price {
  font-size: 38px;
  font-weight: 700;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  color: #454545;
  /* margin: 4px 0; */
}

.sublabel {
  font-size: 25px;
  color: #4545458f;
  font-weight: 500;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.continue-btn {
  background: transparent;
  border: 2px solid #e05145;
  color: #e05145;
  font-size: 16px;
  padding: 12px 36px;
  border-radius: 32px;
  cursor: pointer;
  transition: 0.3s ease;
}

.continue-btn:hover {
  background: #e05145;
  color: white;
}

.bear-image {
  max-width: 480px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bear-subscription-animation {
  width: 450px;
  height: 450px;
  overflow: hidden;
  /* position: absolute; */
  /* top: 310px;
  right: calc(55%); */
}

@media (max-width: 1024px) {
  .pricing-container {
    flex-direction: column;
    padding: 40px 20px;
  }

  .bear-section {
    order: 1;
    /* Show bear first */
  }

  .pricing-content-section {
    order: 2;
  }

  .pricing-content {
    max-width: 100%;
    text-align: center;
  }

  .features {
    text-align: left;
  }

  .headline {
    font-size: 2rem;
  }

  .subhead {
    font-size: 1.1rem;
  }

  /* .bear-subscription-animation {
    width: 450px;
    height: 450px;
  } */
}

@media (max-width: 600px) {
  .plan {
    width: 100%;
    max-width: 320px;
  }

  .pricing-options {
    gap: 16px;
  }

  .continue-btn {
    width: 100%;
    padding: 14px;
    font-size: 1.1rem;
  }

  .headline {
    font-size: 1.8rem;
  }

  .price {
    font-size: 1.8rem;
  }
}

.play-pause {
  cursor: pointer;
  width: 63px !important;
  height: 63px !important;
}

/* ===== countdown ========= */

.parent-count {
  position: absolute;
  left: 7%;
  margin-top: 50px;
}

.counterdown {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}

.counterdown-card {
  width: 104px;
  height: 110px;
  border-radius: 20px;
  /* border: 1px solid var(--border-color); */
  border: 0.55px solid;
  border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #e8e8e8 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-overlay);
}

.countdown {
  font-size: 40px;
  font-weight: 700;
  color: var(--text-color);
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  line-height: 100%;
}

.countdown-label {
  font-size: 20px;
  font-weight: 550;
  color: var(--text-light);
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
}

.count-title {
  text-align: center;
  margin-top: 10px;
  font-size: 25px;
  font-weight: 550;
  color: #454545a8;
  /* font-family: 'Optima Medium', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
}

.subscribe-banner {
  position: sticky;
  bottom: 0;
  z-index: 999;
  width: 100%;
  padding: 22px 0px;
  background: #ffe8e5;
  box-shadow: 9px 0px 4px 0px #e1675880;
  border-top: 2px solid #e16758;
  color: white;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.subscribe-quiz-banner {
  z-index: 999;
  width: 100%;
  padding: 22px 0px;
  background: #ffe8e5;
  box-shadow: 9px 0px 4px 0px #e1675880;
  border-top: 2px solid #e16758;
  color: white;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.subscribe-parent {
  text-align: left;
  display: flex;
  justify-content: space-around;
}

.subscribe-text {
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 25px;
  color: #454545;
}

.subscribe-desc {
  color: #454545cc;
  /* font-family: 'Optima medium'; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 19px;
  margin-top: 5px;
}

.subscribe-description {
  color: #454545cc;
  /* font-family: 'Optima medium'; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 19px;
  margin-top: 5px;
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  line-height: 1.4;
}

.subscribe-description .icon {
  width: 22px;
  height: 22px;
  flex-shrink: 0;
}

.subscribe-description .icon img {
  width: 100%;
  height: auto;
  display: block;
}

/* Mobile-first adjustments */
@media (max-width: 768px) {
  .subscribe-description {
    font-size: 16px;
    gap: 6px;
  }

  .subscribe-description .icon {
    width: 18px;
    height: 18px;
  }

  .subscribe-banner {
    padding: 22px 4px;
  }

  .subscribe-quiz-banner {
    padding: 22px 4px;
  }

  .subscribe-btn {
    width: 100%;
    max-width: none;
    font-size: 18px;
    height: 50px;
  }
}

.subscribe-btn {
  width: 294px;
  height: 70px;
  border-radius: 50px;
  font-size: 25px;
  border: 1px solid var(--primary-color);
  padding: 15px 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14px;
  text-align: center;
  margin: 0 auto;
}

.subscription-btn {
  width: 294px;
  height: 70px;
  border-radius: 50px;
  font-size: 25px;
  border: 1px solid var(--primary-color);
  padding: 15px 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14px;
  text-align: center;
  margin: 0 auto;
}

.subscribe-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

.subscription-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

@media (max-width: 968px) {
  .subscribe-parent {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .text-area {
    width: 100%;
  }

  .subscribe-text {
    font-size: 20px;
    text-align: center !important;
  }

  .subscribe-desc {
    font-size: 16px;
    text-align: center !important;
    margin-bottom: 10px;
  }

  .subscribe-description {
    font-size: 16px;
    text-align: center !important;
    margin-bottom: 10px;
    justify-content: center;
  }

  .btn-area {
    margin: 0 auto;
  }
}

/* Typography */
.title-text {
  font-size: 30px;
  line-height: 130%;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  text-align: center;
  color: var(--text-color);
  margin-bottom: 10px;
}

.sub-desc {
  font-size: 16px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #777980;
  line-height: 30px;
  text-align: center;
  margin-bottom: 20px;
}

.sub-off {
  font-size: 18px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #777980;
  line-height: 30px;
  text-align: center;
  margin-bottom: 15px;
}

.sub-offs {
  font-size: 30px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #454545;
  line-height: 40px;
  text-align: center;
}

.sub-offs span {
  color: #e16758;
}

.sub-off span {
  color: #e16758;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

@media (max-width: 600px) {
  .sub-offs {
    font-size: 20px;
    /* font-family: Optima Bold; */
    font-family: 'DojowellBasic', Georgia;
    font-weight: 700;
    color: #454545;
    line-height: 30px;
    text-align: center;
  }

  .custom-subscribe-modal .ant-modal-content {
    border-radius: 25px !important;
    /* padding: 28px !important; */
    padding: 35px 20px !important;
  }

  .custom-subscribe-modal .ant-modal-body {
    padding: 0;
    /* optional: if you want tighter control */
  }

  .subscribe-button {
    width: 250px !important;
    max-width: none;
    font-size: 18px;
    height: 50px;
  }
}

.subscribe-button {
  width: 250px;
  height: 70px;
  border-radius: 50px;
  font-size: 25px;
  border: 1px solid var(--primary-color);
  padding: 15px 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14px;
  text-align: center;
  margin: 0 auto;
}

.subscribe-button:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

.subscribe-btn {
  width: 294px;
  height: 70px;
  border-radius: 50px;
  font-size: 25px;
  border: 1px solid var(--primary-color);
  padding: 15px 25px;
  /* font-family: 'Optima Bold', Optima, sans-serif; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14px;
  text-align: center;
  margin: 0 auto;
}

.subscribe-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

@media (min-width: 600px) {
  .custom-subscribe-modal .ant-modal-content {
    border-radius: 25px !important;
    /* padding: 28px !important; */
    padding: 55px 42px !important;
  }

  .custom-subscribe-modal .ant-modal-body {
    padding: 0;
    /* optional: if you want tighter control */
  }
}

.sub-features {
  list-style: none;
  padding: 0;
  font-size: 18px;
  line-height: 1.6;
  text-align: left;
  margin: 15px 0px;
}

.sub-features li {
  margin-bottom: 16px;
  line-height: 1.6;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  color: #82858d;
  font-size: 19px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 6px;
}

.fancy-scroll-indicator {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  height: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 20;
}

.scroll-track {
  position: relative;
  width: 4px;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  /* Darker background */
  border-radius: 2px;
  overflow: hidden;
}

.scroll-glow {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 14px;
  height: 14px;
  background: linear-gradient(45deg, #111111, #e27063);
  /* darker + highlight */
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  transition: top 0.3s ease;
  animation: pulse 2s infinite ease-in-out;
}

.scroll-label {
  margin-top: 12px;
  color: #333;
  /* Dark gray text */
  font-size: 14px;
  opacity: 0.8;
  writing-mode: vertical-rl;
  transform: rotate(180deg);
}

@keyframes pulse {
  0% {
    transform: translateX(-50%) scale(1);
  }

  50% {
    transform: translateX(-50%) scale(1.2);
  }

  100% {
    transform: translateX(-50%) scale(1);
  }
}

.scroll-tooltip {
  position: absolute;
  left: 40px;
  top: 30%;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  animation:
    fadeInUp 1s ease,
    blink 2s infinite;
  z-index: 100;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.scroll-gif-hint {
  position: absolute;
  bottom: 40px;
  left: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.6);
  padding: 10px;
  border-radius: 12px;
}

.original-price {
  color: #45454580;
  font-size: 0.9rem;
  text-decoration: line-through;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 25px;
}

.price-div {
  display: flex;
}

.disc-title {
  list-style: none;
  padding: 0;
  font-size: 23px;
  line-height: 1.6;
  text-align: left;
  margin: 15px 0px;
}

.disc-title li {
  margin-bottom: 16px;
  line-height: 1.6;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  color: #82858d;
  font-size: 23px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 6px;
}

.disc-off {
  font-size: 30px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #454545;
  line-height: 40px;
  text-align: center;
}

.disc-off span {
  color: #e16758;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

@media (max-width: 600px) {
  .disc-off {
    font-size: 20px;
    /* font-family: Optima Bold; */
    font-family: 'DojowellBasic', Georgia;
    font-weight: 700;
    color: #454545;
    line-height: 30px;
    text-align: center;
  }

  .disc-icon {
    width: 20px !important;
    flex-shrink: 0;
    margin-top: 0px;
  }
}

.disc-icon {
  width: 27px;
  flex-shrink: 0;
  margin-top: 5px;
}
