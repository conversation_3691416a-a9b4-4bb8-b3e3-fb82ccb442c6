import type { LocalizationResource } from '@clerk/types';
import type { LocalePrefixMode } from 'next-intl/routing';
import { enUS, frFR } from '@clerk/localizations';

const localePrefix: LocalePrefixMode = 'as-needed';

export const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
export const QUIZ_BASE_URL = process.env.NEXT_PUBLIC_QUIZ_API_URL;

// export const BASE_URL = 'https://api.dojowell.com:8484/web/v1/web_user';
// export const BASE_URL = 'http://192.168.29.234:8484/web/v1/web_user';
// export const BASE_URL = 'http://**************:8484/web/v1/web_user';
// export const BASE_URL = 'http://************:8481/web/v1/web_user';
// export const QUIZ_BASE_URL = 'https://api.dojowell.com:8484/web/v1/web_quiz_values';

export const apiUrls = {
  send_otp_email: '/send_otp_email',
  create_register_user: '/create_register_user',
  add_to_waitlist: '/add_to_waitlist',
  verify_email: '/verify_email',
  create_support: '/create_support',
  create_quiz: '/add_quiz_values',
  get_user_active_subscription: '/get_user_active_subscription',
  create_subscription_code: '/create_subscription_code',
};

// FIXME: Update this configuration file based on your project information
export const AppConfig = {
  name: 'Nextjs Starter',
  locales: ['en', 'fr', 'hin', 'chn', 'spa'],
  defaultLocale: 'en',
  localePrefix,
  localeDetection: true,
};

const supportedLocales: Record<string, LocalizationResource> = {
  en: enUS,
  fr: frFR,
};

export const ClerkLocalizations = {
  defaultLocale: enUS,
  supportedLocales,
};
