'use client';

import React, { useEffect, useState } from 'react';

type TimeLeft = {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
};

const CountdownTimer = () => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    const calculateTimeLeft = (): TimeLeft => {
      const targetDate = new Date('2025-07-24T00:00:00');
      const now = new Date();
      const difference = targetDate.getTime() - now.getTime();

      if (difference <= 0) {
        return { days: 0, hours: 0, minutes: 0, seconds: 0 };
      }

      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    };

    setTimeLeft(calculateTimeLeft()); // set immediately on mount
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="counter">
      <div className="count-card">
        <p className="count">{timeLeft.days}</p>
        <p className="count-label">days</p>
      </div>
      <div className="count-card">
        <p className="count">{timeLeft.hours}</p>
        <p className="count-label">hrs</p>
      </div>
      <div className="count-card">
        <p className="count">{timeLeft.minutes}</p>
        <p className="count-label">min</p>
      </div>
      <div className="count-card">
        <p className="count">{timeLeft.seconds}</p>
        <p className="count-label">sec</p>
      </div>
    </div>
  );
};

export default CountdownTimer;
