'use client';

import { CloseOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import Cookies from 'js-cookie';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { apiUrls, BASE_URL } from '@/utils/AppConfig';

const FirstVisitPopup: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const searchParams = useSearchParams();
  const paramUserId = searchParams.get('id');
  const paramEmail = searchParams.get('email');

  useEffect(() => {
    const hasSeenPopup = sessionStorage.getItem('hasSeenSubscribePopup');

    if (hasSeenPopup) {
      return;
    }

    const showPopup = () => {
      sessionStorage.setItem('hasSeenSubscribePopup', 'true');
      const timer = setTimeout(() => {
        setShowModal(true);
      }, 20000); // 30 seconds delay

      return () => clearTimeout(timer);
    };

    const checkAndDecide = async () => {
      let finalUserId: number | null = null;
      // let finalEmail: string | null = null;

      // Check URL params
      if (paramEmail && paramUserId) {
        // finalEmail = paramEmail;
        const parsedId = Number.parseInt(paramUserId, 10);
        finalUserId = !Number.isNaN(parsedId) ? parsedId : null;
      } else {
        // Check cookies
        const cookieEmail = Cookies.get('user_email');
        const cookieUserIdStr = Cookies.get('user_id');
        if (cookieEmail && cookieUserIdStr) {
          // finalEmail = cookieEmail;
          const parsedId = Number.parseInt(cookieUserIdStr, 10);
          finalUserId = !Number.isNaN(parsedId) ? parsedId : null;
        }
      }

      if (finalUserId !== null) {
        try {
          const res = await fetch(
            `${BASE_URL}${apiUrls.get_user_active_subscription}?user_id=${finalUserId}`,
            {
              method: 'GET',
            },
          );

          if (!res.ok) {
            throw new Error('Failed to fetch user subscription');
          }

          const data = await res.json();
          const isActive = data?.data?.is_subscription_active === true;
          Cookies.set('isSubscribe', isActive ? 'true' : 'false');

          if (!isActive) {
            showPopup();
          }
        } catch (err) {
          console.error('Error fetching subscription:', err);
          showPopup();
        }
      } else {
        showPopup();
      }
    };

    checkAndDecide();
  }, [paramEmail, paramUserId]);

  return (
    <Modal
      open={showModal}
      onCancel={() => setShowModal(false)}
      footer={null}
      closable={true}
      centered
      maskClosable={true}
      className="custom-subscribe-modal"
      closeIcon={<CloseOutlined style={{ fontSize: 18, color: '#82858D91' }} />}
    >
      <div>
        {/* <p className="title-text">your core values are clear.</p>
        <p className="sub-desc">Ready to bring them to life inside the app?</p> */}
        <ul className="sub-features">
          <li>
            <div className="icons">
              <img src="/assets/images/leaf.png" alt="audio icon" />
            </div>
            <div className="sub-offs">
              <span>10% off</span>
              {' '}
              your dojowell
              <br />
              {' '}
              journey valid today only.
            </div>
          </li>

          <li>
            {/* <div className="icon">
              <img src="/assets/images/leaf.png" alt="audio icon" />
            </div> */}
            <div className="sub-off">
              complete your path with calm and purpose.
            </div>
          </li>
        </ul>
        <Link href="/subscription" className="subscribe-button">claim my offer</Link>
      </div>
    </Modal>
  );
};

export default FirstVisitPopup;
