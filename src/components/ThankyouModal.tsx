import { Modal } from 'antd';
import Image from 'next/image';
// import { useRouter } from 'next/navigation';
import React from 'react';
import thankYouIcon from '../../public/assets/images/thankyou.svg'; // Adjust path if needed

type ModalProps = {
  thankYouModal: boolean;
  setThankYouModal: React.Dispatch<React.SetStateAction<boolean>>;
};

function ThankyouModal({ thankYouModal, setThankYouModal }: ModalProps) {
  // const router = useRouter();

  // const handlePurchaseClick = () => {
  //   setThankYouModal(false);
  //   setTimeout(() => {
  //     router.push('/subscription');
  //   }, 300);
  // };

  return (
    <div>
      <Modal
        centered
        footer={null}
        closable={false}
        maskClosable={true}
        open={thankYouModal}
        onCancel={() => setThankYouModal(false)}
      >
        <div>
          <div className="thanks-logo">
            <Image src={thankYouIcon} alt="Thank You Icon" />
          </div>

          <p className="titletext">
            Thank you!
          </p>

          <p className="psub">
            We appreciate your interest in DojoWell. You'll be notified as soon as the beta version is released!
          </p>

          {/* <p className="psubtext">
            Would you like to purchase a subscription?
          </p> */}

          {/* <div className="thanksbtn" style={{ marginTop: '40px' }}>
            <button
              className="btndivthankyou"
              onClick={() => setThankYouModal(false)}
              type="button"
            >
              No, thanks
            </button>

            <button
              className="btndivthankyou"
              onClick={handlePurchaseClick}
              type="button"
            >
              Purchase
            </button>
          </div> */}
        </div>
      </Modal>
    </div>
  );
}

export default ThankyouModal;
