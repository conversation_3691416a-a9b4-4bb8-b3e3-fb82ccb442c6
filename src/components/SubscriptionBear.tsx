'use client';

import {
  Alignment,
  Fit,
  Layout,
  useRive,
  useStateMachineInput,
} from '@rive-app/react-canvas';
import React, { useEffect, useState } from 'react';

const STATE_MACHINE_NAME = 'State Machine 1';

type BearProps = {
  emotion: number;
  action: number;
  outfitLevel?: number;
  darkMode?: boolean;
};

const SubscriptionBear = ({
  emotion,
  action,
  outfitLevel,
  darkMode,
}: BearProps) => {
  const [isReady, setIsReady] = useState(false);

  const { rive, RiveComponent } = useRive({
    src: '/assets/animations/bear-test.riv',
    stateMachines: STATE_MACHINE_NAME,
    autoplay: true,
    layout: new Layout({ fit: Fit.Contain, alignment: Alignment.Center }),
    onLoad: () => setIsReady(true),
  });

  const actionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Action');
  const emotionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Emotion');
  const darkModeInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Dark-Mode');
  const outfitLevelInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Outfit-Level');

  // Set Emotion
  useEffect(() => {
    if (isReady && emotionInput) {
      emotionInput.value = emotion;
    }
  }, [isReady, emotionInput, emotion]);

  // Set Action once (let .riv handle reset to 0 internally)
  useEffect(() => {
    if (isReady && actionInput) {
      actionInput.value = action;
    }
  }, [isReady, actionInput, action]);

  // Set Dark Mode
  useEffect(() => {
    if (isReady && darkModeInput !== null && darkMode !== undefined) {
      darkModeInput.value = darkMode;
    }
  }, [isReady, darkModeInput, darkMode]);

  // Set Outfit-Level
  useEffect(() => {
    if (isReady && outfitLevelInput !== null && outfitLevel !== undefined) {
      outfitLevelInput.value = outfitLevel;
    }
  }, [isReady, outfitLevelInput, outfitLevel]);

  return <RiveComponent />;
};

export default SubscriptionBear;

// 'use client';

// import { Alignment, Fit, Layout, useRive, useStateMachineInput } from '@rive-app/react-canvas';
// import React, { useEffect, useState } from 'react';

// const STATE_MACHINE_NAME = 'State Machine 1';

// type BearProps = {
//   emotion: number;
//   action: number;
//   setBearAction?: React.Dispatch<React.SetStateAction<number>>;
// };

// const SubscriptionBear = ({ emotion, action }: BearProps) => {
//   const [isReady, setIsReady] = useState(false);

//   const { rive, RiveComponent } = useRive({
//     src: '/assets/animations/bear-v3.riv',
//     stateMachines: STATE_MACHINE_NAME,
//     autoplay: true,
//     layout: new Layout({ fit: Fit.Contain, alignment: Alignment.Center }),
//     onLoad: () => {
//       setIsReady(true);
//     },
//   });

//   // Numeric inputs
//   const actionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Action');
//   const emotionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Emotion');

//   useEffect(() => {
//     if (isReady && actionInput && emotionInput) {
//       actionInput.value = action;
//       emotionInput.value = emotion;
//     }
//   }, [isReady, actionInput, emotionInput, action, emotion]);

//   return (
//     <RiveComponent />
//   );
// };

// export default SubscriptionBear;
