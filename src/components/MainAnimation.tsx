'use client';

import {
  Alignment,
  Fit,
  Layout,
  useRive,
  useStateMachineInput,
} from '@rive-app/react-canvas';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import AudioAnimation from './AudioAnimations';
import CloudText from './CloudText';

const STATE_MACHINE_NAME = 'State Machine 1';

type AnimationProps = {
  setBearAction: React.Dispatch<React.SetStateAction<number>>;
  setBearPosition: React.Dispatch<React.SetStateAction<number>>;
  setOverflow: React.Dispatch<React.SetStateAction<string>>;
  setBearEmotion: React.Dispatch<React.SetStateAction<number>>;
  bearPosition: number;
};

const MainAnimation = ({
  setBearAction,
  setBearPosition,
  bearPosition,
  setOverflow,
  setBearEmotion,
}: AnimationProps) => {
  const [isReady, setIsReady] = useState(false);
  const [scrollStep, setScrollStep] = useState(0);
  const [showAudio, setShowAudio] = useState(false);
  const [windowWidth, setWindowWidth] = useState(0);
  // const [showScrollHint, setShowScrollHint] = useState(true);

  const t = useTranslations('Exploring');

  const { rive, RiveComponent } = useRive({
    src: '/assets/animations/website.riv',
    stateMachines: STATE_MACHINE_NAME,
    autoplay: true,
    layout: new Layout({
      fit: Fit.Fill,
      alignment: Alignment.Center,
    }),
    onLoad: () => {
      console.warn('Rive file loaded.');
      setIsReady(true);
    },
  });

  const orbsInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Orbs-Visible');
  const matrixInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Matrix-Visible');
  const shiningInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Shining-Visible');
  const leavesInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Leaves-Visible');

  // useEffect(() => {
  //   const handleScroll = (e: WheelEvent) => {
  //     setScrollStep((prev) => {
  //       const next = e.deltaY > 0 ? Math.min(prev + 1, 25) : Math.max(prev - 1, 0);
  //       if (next >= 7) {
  //         setShowScrollHint(false);
  //       }
  //       return next;
  //     });
  //   };

  //   window.addEventListener('wheel', handleScroll);
  //   return () => window.removeEventListener('wheel', handleScroll);
  // }, []);

  // Handle scroll direction
  useEffect(() => {
    const handleScroll = (e: WheelEvent) => {
      setScrollStep((prev) => {
        if (e.deltaY > 0) {
          return Math.min(prev + 1, 25);
        } else {
          return Math.max(prev - 1, 0);
        }
      });
    };

    window.addEventListener('wheel', handleScroll);
    return () => window.removeEventListener('wheel', handleScroll);
  }, []);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);

    if (typeof window !== 'undefined') {
      setWindowWidth(window.innerWidth); // set initial width
      window.addEventListener('resize', handleResize);
    }

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update animation and overflow state on scroll step
  useEffect(() => {
    if (!isReady || !orbsInput || !matrixInput || !shiningInput || !leavesInput) {
      return;
    }

    let responsiveSize = 5;
    if (windowWidth < 1300) {
      responsiveSize = 20;
    } else if (windowWidth > 1300 && windowWidth < 1500) {
      responsiveSize = 20;
    } else if (windowWidth > 1500 && windowWidth < 1700) {
      responsiveSize = 20;
    }

    // Reset all inputs
    orbsInput.value = false;
    matrixInput.value = false;
    shiningInput.value = false;
    leavesInput.value = false;

    let audioModal = false;
    let bearAction = 0;
    let bearEmotion = 0;
    let bearPos = 28;
    let timeoutId: NodeJS.Timeout;

    if (scrollStep >= 5 && scrollStep < 10) {
      orbsInput.value = true;
      bearAction = 1;
      bearEmotion = 1;
      bearPos = 23;
      timeoutId = setTimeout(() => setOverflow('hidden'), 1000);
    } else if (scrollStep >= 10 && scrollStep < 15) {
      matrixInput.value = true;
      bearAction = 0;
      bearEmotion = 0;
      bearPos = 26;
      timeoutId = setTimeout(() => setOverflow('hidden'), 1000);
    } else if (scrollStep >= 15 && scrollStep < 20) {
      shiningInput.value = true;
      bearAction = 0;
      bearEmotion = 0;
      bearPos = 20;
    } else if (scrollStep >= 20 && scrollStep < 25) {
      leavesInput.value = true;
      bearAction = 1;
      bearEmotion = 1;
      bearPos = 27;
    } else if (scrollStep >= 25) {
      audioModal = true;
      bearAction = 1;
      bearEmotion = 1;
      bearPos = 17;
      timeoutId = setTimeout(() => setOverflow('auto'), 1000);
    }

    setShowAudio(audioModal);
    setBearAction(bearAction);
    setBearEmotion(bearEmotion);
    setBearPosition(bearPos - responsiveSize);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [
    scrollStep,
    windowWidth,
    isReady,
    orbsInput,
    matrixInput,
    shiningInput,
    leavesInput,
    setBearAction,
    setBearEmotion,
    setBearPosition,
    setOverflow,
  ]);

  return (
    <div className="animation-container">
      {/* Scroll Progress Bar */}
      <div className="fancy-scroll-indicator">
        <div className="scroll-track">
          <div
            className="scroll-glow"
            style={{ top: `${(scrollStep / 25) * 100}%` }}
          />
        </div>
        {/* <img src="/assets/images/PgehtgEaKt.gif" alt="Scroll down" style={{ width: 60, height: 60 }} /> */}
        <span className="scroll-label">Explore</span>
      </div>

      {/* {scrollStep <= 1 && (
        <div className='fancy-scroll-indicator'>
          <div className="scroll-hint-container">
            <div className="scroll-line">
              <div className="scroll-glow-dot" style={{ top: `${(scrollStep / 25) * 100}%` }} />
            </div>
            <div className="scroll-hint-box">
              <img src="/assets/images/PgehtgEaKt.gif" alt="Scroll down" className="scroll-arrow-gif" />
              <span>Scroll to explore</span>
            </div>
          </div>
        </div>
      )} */}

      <RiveComponent />

      <CloudText
        orbsInput={orbsInput?.value || false}
        matrixInput={matrixInput?.value || false}
        shiningInput={shiningInput?.value || false}
        leavesInput={leavesInput?.value || false}
        showAudio={showAudio}
        bearPosition={bearPosition}
      />

      {showAudio && (
        <div className="audio-modal" style={{ right: windowWidth < 1300 ? '33%' : '' }}>
          <div className="audio-box">
            <Link href="/exploring" className="btndivabc justify-center d-flex align-center">
              {/* Try 1-minute Preview */}
              {t('musicbtn')}
            </Link>
            <div className="mt-2 audio-div" style={{ height: '27%' }}>
              {/* <AudioAnimation value={true} /> */}
              <AudioAnimation />
            </div>
            {/* <div style={{ height: '70%' }}>
              <AudioPlayer />
            </div> */}
          </div>
        </div>
      )}
    </div>
  );
};

export default MainAnimation;
