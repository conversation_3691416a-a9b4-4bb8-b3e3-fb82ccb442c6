/* eslint-disable @next/next/no-html-link-for-pages */
'use client';

import { useLocale, useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import logo from '../../public/assets/images/logo-new.svg';
import logosmall from '../../public/assets/images/smalllogo.svg';

function Header() {
  const [isMobile, setIsMobile] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  // const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('Header');

  // const [scrolled, setScrolled] = useState(false);

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = event.target.value;
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2,3}(?=\/|$)/, '');
    // router.push(`/${newLocale}${pathWithoutLocale.startsWith('/') ? pathWithoutLocale : `/${pathWithoutLocale}`}`);
    const newPath = `/${newLocale}${pathWithoutLocale.startsWith('/') ? pathWithoutLocale : `/${pathWithoutLocale}`}`;

    window.location.href = newPath;
  };

  useEffect(() => {
    const checkWidth = () => {
      setIsMobile(window.innerWidth < 1000);
    };

    checkWidth();
    window.addEventListener('resize', checkWidth);
    return () => window.removeEventListener('resize', checkWidth);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={`header ${scrolled ? 'scrolled' : ''}`}>
      {/* <div className={`header ${scrolled ? 'scrolled' : ''}`}> */}
      <Link href="/" className="header-logo">
        <Image src={isMobile ? logosmall : logo} alt="Logo" className="logo" />
      </Link>
      <div className="btn-container">
        {/* <a href="/quiz" className="quiz-button">{t('quiz_btn')}</a> */}
        {!pathname.includes('/subscription') && (
          <a href="/quiz" className="quiz-button">{t('quiz_btn')}</a>
        )}
        {/* <Link href="/quiz" className="quiz-button">{t('quiz_btn')}</Link> */}
        <select className="language-select" value={locale} onChange={handleLanguageChange} disabled={pathname.includes('/quiz')}>
          <option value="en">{t('english')}</option>
          {/* <option value="fr">{t('french')}</option> */}
          <option value="spa">{t('spanish')}</option>
          {/* <option value="hin">{t('hindi')}</option> */}
        </select>
      </div>
    </div>
  );
}

export default Header;
