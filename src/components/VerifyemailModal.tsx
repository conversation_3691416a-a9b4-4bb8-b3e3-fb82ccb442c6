import { Modal } from 'antd';
import React from 'react';

type ModalProps = {
  verifyModal: boolean;
  setverifyModal: React.Dispatch<React.SetStateAction<boolean>>;
  otp: string;
  setOtp: React.Dispatch<React.SetStateAction<string>>;
  verifyOtp: () => void;
  sendOtp: () => void;
};

function VerifyemailModal({ verifyModal, setverifyModal, otp, setOtp, verifyOtp, sendOtp }: ModalProps) {
  return (
    <Modal
      centered
      footer={null}
      closable={false}
      maskClosable={true}
      open={verifyModal}
      onCancel={() => setverifyModal(false)}
    >
      <p className="titletext">Verify Your Email</p>
      <p className="desc">

        We’ve sent a 6-digit verification code to your email. Please enter the code below to continue.
      </p>
      <input
        className="inputfile"
        placeholder="Enter code"
        value={otp}
        onChange={(e) => {
          const value = e.target.value;
          if (value.length <= 6 && /^\d*$/.test(value)) {
            setOtp(value);
          }
        }}
      />

      <button
        className="submitbtn"
        type="button"
        disabled={!otp.length}
        style={{ opacity: otp.length ? 1 : 0.5, cursor: otp.length ? 'pointer' : 'no-drop' }}
        onClick={() => {
          if (otp.length) {
            verifyOtp();
          }
        }}
      >
        Verify
      </button>
      <button
        type="button"
        className="resend"
        onClick={sendOtp}
        style={{
          background: 'none',
          border: 'none',
          padding: 0,
          color: 'inherit',
          textDecoration: 'underline',
          cursor: 'pointer',
          marginTop: '8px',
        }}
      >
        Resend Code
      </button>
    </Modal>
  );
}

export default VerifyemailModal;
