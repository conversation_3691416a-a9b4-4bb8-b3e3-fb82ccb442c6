'use client';
import { Modal } from 'antd';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import MoonLoader from 'react-spinners/MoonLoader';

type waitListProp = {
  ShowModal: boolean;
  formData: {
    fname: string;
    lname: string;
    email: string;
  };
  setFormData: React.Dispatch<React.SetStateAction<{
    fname: string;
    lname: string;
    email: string;
  }>>;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  sendOtp: () => void;
  loading: boolean;
};

function Wailtlist({ ShowModal, formData, setFormData, setShowModal, sendOtp, loading }: waitListProp) {
  const t = useTranslations('Exploring');

  const [error, setError] = useState({
    fname: '',
    lname: '',
    email: '',
  });

  const formValidation = (): boolean => {
    let isValid = true;
    const errors = {
      fname: '',
      lname: '',
      email: '',
    };

    if (!formData.fname.trim()) {
      errors.fname = 'First name is required';
      isValid = false;
    }

    if (!formData.lname.trim()) {
      errors.lname = 'Last name is required';
      isValid = false;
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/\S[^\s@]*@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Enter a valid email';
      isValid = false;
    }

    setError(errors);
    return isValid;
  };

  const handleSubmit = async () => {
    const isFormValid = formValidation();
    if (!isFormValid) {
      return;
    }
    // setShowModal(false)
    await sendOtp();
  };

  return (
    <div className="mdiv">
      <Modal
        centered
        footer={null}
        closable={false}
        maskClosable={true}
        open={ShowModal}
        onCancel={() => setShowModal(false)}
      >
        <p className="titletext">{t('form_title')}</p>
        {error.fname && <p className="error-text">{error.fname}</p>}
        <input
          className="inputfile"
          value={formData.fname}
          onChange={e => setFormData({ ...formData, fname: e.target.value })}
          placeholder={t('fname_input')}
        />
        {error.lname && <p className="error-text">{error.lname}</p>}
        <input
          className="inputfile"
          value={formData.lname}
          onChange={e => setFormData({ ...formData, lname: e.target.value })}
          placeholder={t('lname_input')}
        />
        {error.email && <p className="error-text">{error.email}</p>}
        <input
          className="inputfile"
          value={formData.email}
          onChange={e => setFormData({ ...formData, email: e.target.value })}
          placeholder={t('email_input')}
        />
        <button
          className="submitbtn"
          type="button"
          onClick={handleSubmit}
          disabled={loading}
        >
          {loading && (
            <MoonLoader color="#e16758" size={22} />
          )}
          <span>{t('form_btn')}</span>
        </button>
      </Modal>
    </div>
  );
}

export default Wailtlist;
