'use client';

import { Alignment, Fit, Layout, useRive, useStateMachineInput } from '@rive-app/react-canvas';
import React, { useEffect, useState } from 'react';

const STATE_MACHINE_NAME = 'State Machine 1';

type BearProps = {
  emotion: number;
  action: number;
  setBearAction?: React.Dispatch<React.SetStateAction<number>>;
};

const FramerBear = ({ emotion, action }: BearProps) => {
  const [isReady, setIsReady] = useState(false);

  const { rive, RiveComponent } = useRive({
    src: '/assets/animations/bear-farmer.riv',
    stateMachines: STATE_MACHINE_NAME,
    autoplay: true,
    layout: new Layout({ fit: Fit.Contain, alignment: Alignment.Center }),
    onLoad: () => {
      setIsReady(true);
    },
  });

  // Numeric inputs
  const actionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Action');
  const emotionInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'Emotion');

  useEffect(() => {
    if (isReady && actionInput && emotionInput) {
      actionInput.value = action;
      emotionInput.value = emotion;
    }
  }, [isReady, actionInput, emotionInput, action, emotion]);

  return (
    <RiveComponent />
  );
};

export default FramerBear;
