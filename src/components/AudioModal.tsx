'use client';

import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';

type AudioPlayerProps = {
  onPlayChange?: (playing: boolean) => void;
};

const audioTracks = [
  {
    src: '/assets/audio/010_Preview01.aac',
    captions: '/assets/audio/010_Preview01.aac',
  },
  {
    src: '/assets/audio/020_Preview02.aac',
    captions: '/assets/audio/020_Preview02.aac',
  },
];

type LoopMode = 'off' | 'playlist' | 'track';

const AudioPlayer = ({ onPlayChange }: AudioPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isShuffle, setIsShuffle] = useState(false);
  const [loopMode, setLoopMode] = useState<LoopMode>('off');

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const newPlayState = !isPlaying;
    newPlayState ? audio.play() : audio.pause();
    setIsPlaying(newPlayState);
    onPlayChange?.(newPlayState);
  };

  const toggleLoopMode = () => {
    setLoopMode(prev =>
      prev === 'off' ? 'playlist' : prev === 'playlist' ? 'track' : 'off',
    );
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = Number.parseFloat(e.target.value);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setProgress(time);
    }
  };

  const handleDotClick = (index: number) => {
    if (index === currentTrackIndex) {
      return;
    }
    setCurrentTrackIndex(index);
    setProgress(0);
    setDuration(0);
    setIsPlaying(false);
  };

  const handleNext = () => {
    let nextIndex;

    if (isShuffle) {
      do {
        nextIndex = Math.floor(Math.random() * audioTracks.length);
      } while (nextIndex === currentTrackIndex && audioTracks.length > 1);
    } else {
      nextIndex = (currentTrackIndex + 1) % audioTracks.length;
    }

    setCurrentTrackIndex(nextIndex);
    setProgress(0);
    setIsPlaying(true);
    onPlayChange?.(true);
  };

  const handlePrevious = () => {
    const prevIndex = (currentTrackIndex - 1 + audioTracks.length) % audioTracks.length;
    setCurrentTrackIndex(prevIndex);
    setProgress(0);
    setIsPlaying(true);
    onPlayChange?.(true);
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const updateProgress = () => {
      setProgress(audio.currentTime);
    };

    // const updateDuration = () => {
    //   const waitForAccurateDuration = async () => {
    //     try {
    //       const originalTime = audio.currentTime;
    //       audio.currentTime = 999999;
    //       await new Promise((res) => {
    //         const handler = () => {
    //           res(null);
    //           audio.removeEventListener('timeupdate', handler);
    //         };
    //         audio.addEventListener('timeupdate', handler);
    //       });
    //       setDuration(audio.currentTime);
    //       audio.currentTime = originalTime;
    //     } catch {
    //       setDuration(audio.duration);
    //     }
    //   };
    //   waitForAccurateDuration();
    // };

    const updateDuration = () => {
      const audio = audioRef.current;
      if (!audio) {
        return;
      }

      if (!Number.isNaN(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
      }
    };

    const handleEnded = () => {
      if (!audioRef.current) {
        return;
      }

      // Loop current track
      if (loopMode === 'track') {
        audioRef.current.currentTime = 0;
        audioRef.current.play();
        return;
      }

      let nextIndex;

      if (isShuffle) {
        do {
          nextIndex = Math.floor(Math.random() * audioTracks.length);
        } while (nextIndex === currentTrackIndex && audioTracks.length > 1);
      } else {
        nextIndex = currentTrackIndex + 1;
      }

      if (nextIndex >= audioTracks.length) {
        if (loopMode === 'playlist') {
          nextIndex = 0;
        } else {
          setIsPlaying(false);
          onPlayChange?.(false);
          return;
        }
      }

      setCurrentTrackIndex(nextIndex);
      setProgress(0);
      setIsPlaying(true);
      onPlayChange?.(true);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentTrackIndex, isShuffle, loopMode]);

  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.load();
      if (isPlaying) {
        audio.play();
      }
    }
  }, [currentTrackIndex]);

  return (
    <div className="audio-player">
      <audio
        ref={audioRef}
        preload="metadata"
        loop={loopMode === 'track'}
      >
        <source src={audioTracks[currentTrackIndex]?.src ?? ''} type="audio/mp3" />
        <track
          kind="captions"
          src={audioTracks[currentTrackIndex]?.captions ?? ''}
          label="English captions"
          default
        />
      </audio>

      <div className="progress-row">
        <div className="d-flex justify-between">
          <span>{progress.toFixed(2)}</span>
          <span>{duration.toFixed(2)}</span>
        </div>
        <input
          type="range"
          min={0}
          max={duration}
          step={0.01}
          value={progress}
          onChange={handleSeek}
          className="progress-input"
        />
      </div>

      <div className="controls">
        <button type="button" onClick={() => setIsShuffle(s => !s)} className={isShuffle ? 'active-shuffle' : ''}>
          <Image src={isShuffle ? '/assets/images/shuffle-on.svg' : '/assets/images/shuffle.png'} alt="shuffle" width={24} height={24} />
        </button>

        <button type="button" onClick={handlePrevious}>
          <Image src="/assets/images/prev.png" alt="prev" width={24} height={24} />
        </button>

        <button type="button" onClick={togglePlay}>
          <Image
            src={isPlaying ? '/assets/images/pause.png' : '/assets/images/play.png'}
            alt="play-pause"
            width={63}
            height={63}
            className="play-pause"
          />
        </button>

        <button type="button" onClick={handleNext}>
          <Image src="/assets/images/next.png" alt="next" width={24} height={24} />
        </button>

        <button type="button" onClick={toggleLoopMode}>
          <Image
            src={
              loopMode === 'off'
                ? '/assets/images/group.png'
                : loopMode === 'playlist'
                  ? '/assets/images/repeat_playlist.svg'
                  : '/assets/images/repeat_one.svg'
            }
            alt="loop"
            width={24}
            height={24}
          />
        </button>
      </div>

      <div className="carousal-dots">
        {audioTracks.map((_, index) => (
          <button
            type="button"
            key={index}
            className={`carousal-dot ${index === currentTrackIndex ? 'carousal-dot-active' : ''}`}
            onClick={() => handleDotClick(index)}
            aria-label={`Select track ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default AudioPlayer;
