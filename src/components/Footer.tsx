/* eslint-disable @next/next/no-html-link-for-pages */

import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import thankYouIcon from '../../public/assets/images/Youtube.svg';

const Footer = () => {
  return (
    <div className="footer">
      <div className="footer-text">© 2025 DojoWell. All rights reserved.</div>
      <div className="footer-tabs">
        {/* <Link href="/support" className="footer-text">Support & FAQs</Link> */}
        <a href="/faqs" className="footer-text">Support & FAQs</a>

        {' '}
        |
        <Link className="footer-text" href="/Privacy_Policy">Privacy Policy</Link>
        {' '}
        |
        <Link className="" href="/terms_and_conditions">Terms and conditions</Link>
        <Link className="" target="_blank" href="https://youtu.be/VQObsy-1mac?si=Uru_HZAwn_oD2PNa">
          <Image src={thankYouIcon} alt="logo" width={23} height={23} className="footer-youtube" />
        </Link>
      </div>
    </div>
  );
};

export default Footer;
