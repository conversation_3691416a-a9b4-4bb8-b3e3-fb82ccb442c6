// app/components/PaddleProvider.tsx
'use client';

import { useEffect } from 'react';

// declare global {
//   interface Window {
//     Paddle: any;
//   }
// }

type CustomWindow = typeof window & {
  Paddle: any;
};

export default function PaddleProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
    script.async = true;
    // script.onload = () => {
    //   if (typeof window !== "undefined" && window.Paddle) {
    //     window.Paddle.Environment.set("sandbox"); // Use "sandbox" or "production"
    //     window.Paddle.Setup({ token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN! });
    //   }
    // };
    script.onload = () => {
      const customWindow = window as CustomWindow;
      if (customWindow.Paddle) {
        // console.log('🧠 Paddle script loaded.');
        customWindow.Paddle.Environment.set('sandbox');
        customWindow.Paddle.Setup({ token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN! });
        // console.log('✅ Paddle setup complete.');
      } else {
        console.error('❌ Paddle not available after script load.');
      }
    };

    document.body.appendChild(script);
  }, []);

  return <>{children}</>;
}
