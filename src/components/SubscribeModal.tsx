import { CloseOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import Link from 'next/link';
import React from 'react';

type ModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

function SubscribeModal({ isOpen, onClose }: ModalProps) {
  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      closable={true}
      centered
      maskClosable={true}
      className="custom-subscribe-modal"
      closeIcon={
        <CloseOutlined style={{ fontSize: 18, color: '#82858D91' }} />
      }
    >
      <div>
        <p className="title-text">your core values are clear.</p>
        <p className="sub-desc">Ready to bring them to life inside the app?</p>
        <ul className="sub-features">
          <li>
            <div className="icon">
              <img src="/assets/images/leaf.png" alt="audio icon" />
            </div>
            <div className="sub-off">
              get
              {' '}
              <span>10% off</span>
              {' '}
              if you subscribe today.
            </div>
          </li>
        </ul>

        {/* <p className="sub-off"><img src="/assets/images/leaf.png" alt="audio icon" /> Get <span>10% off</span> if you subscribe today.</p> */}
        <Link href="/subscription" className="subscribe-btn">unlock full journey</Link>
      </div>
    </Modal>
  );
}

export default SubscribeModal;
