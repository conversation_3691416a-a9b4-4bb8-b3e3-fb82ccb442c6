'use client';

import { Alignment, Fit, Layout, useRive, useStateMachineInput } from '@rive-app/react-canvas';
import React, { useEffect, useState } from 'react';
import AudioPlayer from './AudioModal';

const STATE_MACHINE_NAME = 'State Machine 1';

// type BearProps = {
//   value: boolean;
// };

const AudioAnimation = () => {
  const [isReady, setIsReady] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);

  const { rive, RiveComponent } = useRive({
    src: '/assets/animations/audiowave.riv',
    stateMachines: STATE_MACHINE_NAME,
    autoplay: true,
    layout: new Layout({ fit: Fit.Contain, alignment: Alignment.Center }),
    onLoad: () => {
      setIsReady(true);
    },
  });

  // Numeric inputs
  const isPlayingInput = useStateMachineInput(rive, STATE_MACHINE_NAME, 'is_Playing');

  useEffect(() => {
    if (isReady && isPlayingInput) {
      isPlayingInput.value = isAudioPlaying;
    }
  }, [isReady, isPlayingInput, isAudioPlaying]);

  return (
    <>
      <RiveComponent />
      <div style={{ height: '70%' }}>
        <AudioPlayer onPlayChange={setIsAudioPlaying} />
      </div>
    </>
  );
};

export default AudioAnimation;
