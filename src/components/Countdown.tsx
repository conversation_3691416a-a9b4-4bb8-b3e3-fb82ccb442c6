'use client';

import { useLocale } from 'next-intl';
import React, { useEffect, useState } from 'react';
import { countdownDates } from '@/libs/countdownConfig';

type TimeLeft = {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
};

const Countdown = () => {
  const locale = useLocale();

  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    let isMounted = true;

    const dateString = (countdownDates[locale as keyof typeof countdownDates] ?? countdownDates.default) as string;
    const targetDate = new Date(dateString);

    const calculateTimeLeft = (): TimeLeft => {
      const now = new Date();
      const difference = targetDate.getTime() - now.getTime();

      if (difference <= 0) {
        return { days: 0, hours: 0, minutes: 0, seconds: 0 };
      }

      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    };

    if (isMounted) {
      setTimeLeft(calculateTimeLeft());
    }

    const timer = setInterval(() => {
      if (isMounted) {
        setTimeLeft(calculateTimeLeft());
      }
    }, 1000);

    return () => {
      isMounted = false;
      clearInterval(timer);
    };
  }, [locale]);

  return (
    <div className="counterdown">
      <div className="counterdown-card">
        <p className="countdown">{timeLeft.days}</p>
        <p className="countdown-label">days</p>
      </div>
      <div className="counterdown-card">
        <p className="countdown">{timeLeft.hours}</p>
        <p className="countdown-label">hrs</p>
      </div>
      <div className="counterdown-card">
        <p className="countdown">{timeLeft.minutes}</p>
        <p className="countdown-label">min</p>
      </div>
      <div className="counterdown-card">
        <p className="countdown">{timeLeft.seconds}</p>
        <p className="countdown-label">sec</p>
      </div>
    </div>
  );
};

export default Countdown;
