import { useTranslations } from 'next-intl';
import React from 'react';

type CloudTexts = {
  orbsInput: boolean | number;
  matrixInput: boolean | number;
  shiningInput: boolean | number;
  leavesInput: boolean | number;
  showAudio: boolean | number;
  bearPosition: number;
};

const CloudText = ({ orbsInput, matrixInput, shiningInput, leavesInput, showAudio, bearPosition }: CloudTexts) => {
  const t = useTranslations('Exploring');

  return (
    <>
      <div
        className="cloud-text"
        style={{
          opacity: (!orbsInput && !matrixInput && !shiningInput && !leavesInput && !showAudio) ? 1 : 0,
          left: `${bearPosition + 10}%`,
        }}
      >
        {t('cloudtext1')}
      </div>
      <div
        className="cloud-text"
        style={{
          opacity: (orbsInput) ? 1 : 0,
          left: `${bearPosition + 10}%`,
        }}
      >
        {t('cloudtext2')}
      </div>
      <div
        className="cloud-text"
        style={{
          opacity: (matrixInput) ? 1 : 0,
          left: `${bearPosition + 10}%`,
        }}
      >
        {t('cloudtext3')}
      </div>
      <div
        className="cloud-text"
        style={{
          opacity: (shiningInput) ? 1 : 0,
          left: `${bearPosition + 10}%`,
        }}
      >
        {t('cloudtext4')}
      </div>
      <div
        className="cloud-text"
        style={{
          opacity: (leavesInput) ? 1 : 0,
          left: `${bearPosition + 10}%`,
        }}
      >
        {t('cloudtext5')}
      </div>
      <div
        className="cloud-text"
        style={{
          opacity: (showAudio) ? 1 : 0,
          top: '43%',
          left: `${bearPosition + 13}%`,
          width: '282px',
          height: '140px',
        }}
      >
        {t('cloudtext6')}
      </div>
    </>
  );
};

export default CloudText;
