// libs/fetchPaddlePrices.ts
export async function fetchPaddlePrices() {
  const API = process.env.NEXT_PUBLIC_PADDLE_API!;
  const token = process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY!;

  const res = await fetch(`${API}/prices`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    cache: 'no-store',
  });

  if (!res.ok) {
    const error = await res.json();
    console.error('Paddle API error:', error);
    throw new Error('Failed to fetch prices');
  }

  const result = await res.json();
  return result.data;
}
