import type { MetadataRoute } from 'next';
import { getBaseUrl } from '@/utils/Helpers';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = getBaseUrl();

  const pages = [
    '/',
    '/exploring',
    '/support',
    '/faqs',
    '/quiz',
    '/subscription',
    '/privacy_and_policy',
    '/privacy_and_policy_dark',
    '/refund_policy',
    '/terms_and_conditions',
    '/terms_and_conditions_dark',
  ];

  return pages.map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'monthly',
    priority: path === '/' ? 0.7 : 0.5,
  }));
}
