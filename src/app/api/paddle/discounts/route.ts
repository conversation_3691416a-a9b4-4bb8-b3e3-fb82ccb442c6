// app/api/paddle/discounts/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  const PADDLE_API = process.env.NEXT_PUBLIC_PADDLE_API;
  const PADDLE_KEY = process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY;

  try {
    const res = await fetch(`${PADDLE_API}/discounts`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PADDLE_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!res.ok) {
      const error = await res.json();
      return NextResponse.json({ error }, { status: res.status });
    }

    const result = await res.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Paddle Discount Proxy Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
