'use client';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { NextIntlClientProvider } from 'next-intl';
import dynamic from 'next/dynamic';
import { notFound } from 'next/navigation';
import { ToastContainer } from 'react-toastify';
import { AppConfig } from '@/utils/AppConfig';
import '../../styles/global.css';
import '../../../public/fonts/style.css';
import '../../styles/global.css';
import 'react-toastify/dist/ReactToastify.css';

const PaddleProvider = dynamic(() => import('@/components/PaddleProvider'), { ssr: false });

type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

export default async function LocaleLayout({ children, params: { locale } }: Props) {
  if (!AppConfig.locales.includes(locale)) {
    notFound();
  }

  const messages = (await import(`../../locales/${locale}.json`)).default;

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <PaddleProvider>
            <GoogleOAuthProvider clientId="226736034861-0mi214csdj9lted0e5sir0b25mjfcat2.apps.googleusercontent.com">
              {/* <Header /> */}
              {children}
            </GoogleOAuthProvider>
            <ToastContainer />
          </PaddleProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
