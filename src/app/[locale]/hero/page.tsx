import Link from 'next/link';
import React from 'react';
import FramerBear from '@/components/FramerBear';
import Header from '@/components/Header';

const Hero = () => {
  return (
    <div className="hero-section">
      <Header />
      <div className="bear-animation">
        <FramerBear action={1} emotion={1} />
      </div>
      <div className="hero-content">
        <p className="hero-title">
          <span style={{ color: '#e16758' }}>dojowell </span>
          {' '}
          – Escape the Noise. Enter the Meaning.
        </p>
        <p className="join-desc">Begin your personal wellness journey guided by a wise companion.</p>
        <Link type="button" href="exploring" className="btndiv justify-center d-flex align-center">Start Exploring</Link>
      </div>
    </div>
  );
};

export default Hero;
