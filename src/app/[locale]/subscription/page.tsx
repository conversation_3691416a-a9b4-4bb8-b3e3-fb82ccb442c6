'use client';
import Cookies from 'js-cookie';
import dynamic from 'next/dynamic';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import <PERSON><PERSON>oader from 'react-spinners/MoonLoader';
import { toast } from 'react-toastify';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import { apiUrls, BASE_URL } from '@/utils/AppConfig';

const SubscriptionBear = dynamic(() => import('@/components/SubscriptionBear'), { ssr: false });

type Price = {
  id: string;
  name: string;
  description: string;
  unit_price: {
    amount: string;
    currency_code: string;
  };
  billing_cycle: {
    interval: string;
    frequency: number;
  } | null;
};

type Discount = {
  id: string;
  code: string;
  type: string;
  amount: string;
  restrict_to: string[];
  enabled_for_checkout: boolean;
  status: string;
};

// declare global {
//   interface Window {
//     PADDLE_INITIALIZED?: boolean;
//   }
// }

type CustomWindow = typeof window & {
  Paddle: any;
};

// type User = {
//   email_address: string;
//   [key: string]: any;
// };

const Subscription = () => {
  const [products, setProducts] = useState<Price[]>([]);
  const [subscriptions, setSubscriptions] = useState<Record<string, any>>({});
  // const [user, setUser] = useState<User | null>(null);
  const [paddleSubscription, setPaddleSubscription] = useState<any | null>(null);
  const [subscriptionPriceid, setSubscriptionPriceid] = useState<any | null>(null);
  const [subscriptionid, setSubscriptionid] = useState<any | null>(null);
  const [platform, setPlatform] = useState<any | null>(null);
  // const [ispayment, setIsPayemnt] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [loader, setLoader] = useState(true);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  // const [appuserId, setAppUserId] = useState<AppUserId | null>(null);

  const router = useRouter();

  const [selectedPlan, setSelectedPlan] = useState<Price | null>(null);
  // const appid = appuserId?.id;

  const [email, setEmail] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  const searchParams = useSearchParams();
  const paramUserId = searchParams.get('id');
  const paramEmail = searchParams.get('email');

  useEffect(() => {
    if (paramEmail && paramUserId) {
      setEmail(paramEmail);

      const parsedParamId = Number.parseInt(paramUserId, 10);
      setUserId(!Number.isNaN(parsedParamId) ? parsedParamId : null);
    } else {
      const cookieEmail = Cookies.get('user_email');
      const cookieUserIdStr = Cookies.get('user_id');

      if (cookieEmail && cookieUserIdStr) {
        setEmail(cookieEmail);

        const parsedCookieId = Number.parseInt(cookieUserIdStr, 10);
        setUserId(!Number.isNaN(parsedCookieId) ? parsedCookieId : null);
      }
    }
  }, [paramEmail, paramUserId]);

  const customWindow = window as CustomWindow;
  const getPaddle = (): CustomWindow['Paddle'] | undefined => {
    return (window as CustomWindow).Paddle;
  };

  // console.log('paddleSubscription', paddleSubscription);
  const Paddle = getPaddle();

  const API = process.env.NEXT_PUBLIC_PADDLE_API;
  const token2 = process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY;
  const projectId = process.env.NEXT_PUBLIC_REVENUECAT_PROJECT_ID;
  const secret_apikey = process.env.NEXT_PUBLIC_REVENUECAT_SECRET_API;
  // const API_KEY = process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API;

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        // const res = await fetch(`${API}/prices`, {
        //   method: 'GET',
        //   headers: {
        //     'Authorization': `Bearer ${process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY}`,
        //     'Content-Type': 'application/json',
        //   },
        // });
        const res = await fetch('/api/paddle/prices');
        if (!res.ok) {
          const error = await res.json();
          console.error('API error:', error);
          return;
        }

        const result = await res.json();
        const sortedProducts = result.data.sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

        setProducts(sortedProducts);
      } catch (err) {
        console.error('Network error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    setLoader(true);
    const fetchDiscounts = async () => {
      try {
        // const res = await fetch(`${API}/discounts`, {
        //   method: 'GET',
        //   headers: {
        //     'Authorization': `Bearer ${token2}`,
        //     'Content-Type': 'application/json',
        //   },
        // });
        const res = await fetch('/api/paddle/discounts');

        if (!res.ok) {
          const error = await res.json();
          console.error('Discount API error:', error);
          return;
        }

        const result = await res.json();

        const activeDiscounts = result.data.filter((d: Discount) =>
          d.status === 'active'
          && d.enabled_for_checkout
          && d.code,
        );
        activeDiscounts.sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

        setDiscounts(activeDiscounts);
      } catch (err) {
        console.error('Discount fetch error:', err);
      } finally {
        setLoader(false);
      }
    };

    if (products.length > 0) {
      fetchDiscounts();
    }
  }, [products]);

  // console.log('products', products);

  useEffect(() => {
    if (!userId) {
      return;
    }
    const handler = async (event: MessageEvent) => {
      // console.log('event', event)
      if (
        event.origin.includes('paddle.com')
        && event.data?.event_name === 'checkout.completed'
      ) {
        const checkoutId = event.data?.callback_data?.data?.transaction_id;
        const email = event.data?.callback_data?.data?.customer?.email;
        const app_user_id = event.data?.callback_data?.data?.custom_data?.app_user_id;

        // console.log('eventeventeventeventeventevent', event);

        if (!checkoutId) {
          toast.error('Missing checkout ID');
          return;
        }

        try {
          const res = await fetch(`${API}/transactions/${checkoutId}`, {
            headers: {
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY}`,
              'Content-Type': 'application/json',
            },
          });

          const data = await res.json();

          if (data?.data?.status === 'paid') {
            toast.success('Payment verified!');

            customWindow.Paddle.Checkout?.close?.();
            const overlayCloseBtn = document.querySelector('.paddle-overlay-close') as HTMLElement;
            if (overlayCloseBtn) {
              overlayCloseBtn.click();
            }
            // console.log('userIduserIduserIduserId', userId);

            // setTimeout(async () => {
            //   try {
            //     const backendRes = await fetch(`${BASE_URL}${apiUrls.create_subscription_code}`, {
            //       method: 'POST',
            //       headers: {
            //         'Content-Type': 'application/json',
            //       },
            //       body: JSON.stringify({
            //         user_id: Number(userId),
            //         transaction_id: checkoutId,
            //       }),
            //     });

            //     if (!backendRes.ok) {
            //       const errRes = await backendRes.json();
            //       console.error('Backend error:', errRes);
            //       toast.error('Failed to register subscription code');
            //     }
            //   } catch (err) {
            //     console.error('❌ Backend API error:', err);
            //     toast.error('Failed to call backend.');
            //   }
            // }, 15000);

            try {
              await fetch(`https://api.revenuecat.com/v2/projects/${projectId}/customers/${app_user_id}/attributes`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${secret_apikey}`,
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
                body: JSON.stringify({
                  attributes: [
                    {
                      name: 'email',
                      value: email,
                    },
                  ],
                }),
              });
              // if (revenueCatRes.ok) {
              //   toast.success("RevenueCat attributes synced!");
              // } else {
              //   toast.error("RevenueCat API error");
              // }
            } catch (err) {
              console.error('❌ API error:', err);
              toast.error('Failed to sync with RevenueCat.');
            }
          }
        } catch (err) {
          console.error('❌ API error:', err);
          toast.error('Failed to fetch checkout.');
        }
      }
    };

    window.addEventListener('message', handler);
    return () => window.removeEventListener('message', handler);
  }, [userId]);

  // console.log('subscriptions', subscriptions)
  // console.log('selectedPlan?.id', selectedPlan?.id)

  const fetchUser = async () => {
    try {
      // const usedToken = paramToken || token;
      const res = await fetch(`${BASE_URL}${apiUrls.get_user_active_subscription}?user_id=${userId}`, {
        method: 'GET',
        // headers: {
        //   'Authorization':
        //     `Bearer ${usedToken}`,
        //   'Content-Type': 'application/json',
        // },
      });

      if (!res.ok) {
        throw new Error('Failed to fetch user');
      }

      const data = await res.json();
      setSubscriptions(data?.data?.subscription_data);
      // setIsPayemnt(data?.data);

      if (data?.data?.is_subscription_active === true) {
        setPaddleSubscription(data?.data?.subscription_data);
        setSubscriptionPriceid(data?.data?.subscription_data?.product_id);
        setSubscriptionid(data?.data?.subscription_data?.subscription_id);
        setPlatform(data?.data?.subscription_data?.platform);

        // const customersRes = await fetch(`${API_KEY}/customers`, {
        //   method: 'GET',
        //   headers: {
        //     'Authorization': `Bearer ${process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY}`,
        //     'Content-Type': 'application/json',
        //   },
        // });

        // if (!customersRes.ok) {
        //   throw new Error('Failed to fetch Paddle customers');
        // }

        // const customersData = await customersRes.json();
        // const paddleCustomer = customersData.data.find((c: any) => c.email === email);

        // if (!paddleCustomer) {
        //   console.warn('No Paddle customer found with email:', email);
        //   return;
        // }

        // const paddleResponse = await fetch(`${API_KEY}/subscriptions?status=active&customer_id=${paddleCustomer.id}`, {
        //   method: 'GET',
        //   headers: {
        //     'Authorization': `Bearer ${process.env.NEXT_PUBLIC_PADDLE_SANDBOX_API_KEY}`,
        //     'Content-Type': 'application/json',
        //   },
        // });

        // if (!paddleResponse.ok) {
        //   throw new Error('Failed to fetch Paddle subscription');
        // }

        // const paddleData = await paddleResponse.json();
        // console.log('Paddle Subscription:', paddleData?.data[0]);
        // const activeSubscription = paddleData?.data?.[0];

        // if (activeSubscription) {
        //   console.log('Paddle Subscription:', activeSubscription);
        //   setPaddleSubscription(activeSubscription);
        // }
      }
    } catch (err) {
      console.error('Error fetching user:', err);
    }
  };

  useEffect(() => {
    if (userId !== null) {
      fetchUser();
    }
  }, [userId]);

  useEffect(() => {
    if (paddleSubscription && products.length > 0) {
      const activePriceId = subscriptionPriceid;
      const matchedProduct = products.find(p => p.id === activePriceId);

      if (matchedProduct) {
        setSelectedPlan(matchedProduct);
      }
    }
  }, [paddleSubscription, products]);

  const cancelSubscription = async () => {
    if (!subscriptionid) {
      toast.info('No active subscription to cancel.');
      return;
    }

    try {
      const res = await fetch(`${API}/subscriptions/${subscriptionid}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token2}`,
          'Content-Type': 'application/json',
        },
        // body: JSON.stringify({
        //   effective_from: 'immediately',
        // }),
      });

      if (!res.ok) {
        const err = await res.json();
        console.error('Cancel error:', err);
        if (err?.error?.code === 'subscription_locked_pending_changes') {
          toast.info('This subscription already has a pending change. You’ll be able to make updates after that change is completed.');
        } else {
          toast.error('Failed to cancel subscription.');
        }
        return;
      }

      toast.success('Subscription successfully cancelled.');
      // Optionally refetch subscription status here
    } catch (err) {
      console.error('Cancel error:', err);
      toast.error('Something went wrong while canceling.');
    }
  };

  // console.log('platform', platform);
  return (
    <>
      <div className="main-div">
        <Header />
        <div className="main-subscription-container">
          <div className="">
            <div className="pricing-container">
              <div className="pricing-content-section">
                <div className="pricing-content">
                  <h1 className="headline">
                    <span className="highlight">your inner peace, reimagined</span>
                  </h1>
                  <p className="subhead">Break free from overwhelm and begin your healing journey with dojowell.</p>

                  <ul className="features">
                    <li>
                      <div className="icon">
                        <img src="/assets/images/music2.png" alt="audio icon" />
                      </div>
                      <div className="text">
                        <strong>600+ audio sessions</strong>
                        {' '}
                        guided journeys, meditations, walking, and reflections by dojo the bear.
                      </div>
                    </li>
                    <li>
                      <img src="/assets/images/leaf.png" alt="leaf icon" className="icon" />
                      <span>
                        <strong className="feature-span">unlimited habits</strong>
                        {' '}
                        go beyond the 3-habit cap — track as many as you need.
                      </span>
                    </li>
                    <li>
                      <img src="/assets/images/diamond.png" alt="lock icon" className="icon" />
                      <span>
                        <strong className="feature-span">full values access</strong>
                        {' '}
                        unlock all 48 values to reflect your true purpose.
                      </span>
                    </li>
                    <li>
                      <img src="/assets/images/deeper.png" alt="rocket icon" className="icon" />
                      <span>
                        <strong className="feature-span">deeper progression</strong>
                        {' '}
                        access hidden orbs, bonus content, and advanced reflections.
                      </span>
                    </li>
                  </ul>

                  {discounts.length > 0 && (
                    <ul className="disc-title">
                      <li>
                        <div className="disc-icon">
                          <img src="/assets/images/leaf.png" alt="audio icon" />
                        </div>
                        <div className="disc-off">
                          get
                          {' '}
                          <span>
                            {discounts[0]?.amount ?? '10'}
                            % off
                          </span>
                          {' '}
                          if you subscribe today.
                        </div>
                      </li>
                    </ul>
                  )}

                  {(loading && loader) || discounts.length === 0
                    ? (
                        <div className="loading-wrapper">
                          <MoonLoader color="#e16758" size={22} />
                        </div>
                      )
                    : products.length === 0
                      ? (
                          <p className="no-subscriptions-message">
                            No subscription plans available at the moment.
                          </p>
                        )
                      : (
                          <div className="pricing-options">
                            {products.map((product) => {
                            // const isSelected = selectedPlan?.id === product.id;
                              const isSelected = selectedPlan?.id === product.id;

                              const matchedDiscount = discounts.find(
                                d =>
                                  d.restrict_to.includes(product.id)
                                  && d.type === 'percentage'
                                  && d.amount,
                              );

                              const originalPrice = Number.parseFloat(product.unit_price.amount) / 100;
                              const discountPercentage = matchedDiscount ? Number.parseFloat(matchedDiscount.amount) : 0;
                              const discountedPrice = originalPrice * (1 - discountPercentage / 100);

                              return (
                                <button
                                  key={product.id}
                                  type="button"
                                  // className={`plan ${isSelected ? 'selected' : ''}`}
                                  className={`plan ${isSelected ? 'selected' : ''}`}
                                  // onClick={() => setSelectedPlan(product)}
                                  onClick={() => {
                                    if (!subscriptions || !subscriptions.product_type) {
                                      setSelectedPlan(product);
                                      return;
                                    }

                                    const normalizeName = (name: string = '') =>
                                      name.toLowerCase().includes('month')
                                        ? 'monthly'
                                        : name.toLowerCase().includes('year')
                                          ? 'yearly'
                                          : name.toLowerCase().includes('life')
                                            ? 'lifetime'
                                            : name.toLowerCase();

                                    const currentType = normalizeName(subscriptions.product_type);
                                    const selectedType = normalizeName(product.name);

                                    const activePriceId = subscriptionPriceid;

                                    if (activePriceId === product.id) {
                                      toast.info(`You're already subscribed to the ${product.name} plan.`);
                                      return;
                                    }

                                    // Block if user has Lifetime and tries to select anything else
                                    if (currentType === 'lifetime' && selectedType !== 'lifetime') {
                                      toast.info('You already own a Lifetime plan.');
                                      return;
                                    }

                                    // if (currentType === selectedType) {
                                    //   toast.info(`You already have the ${product.name} plan.`);
                                    //   return;
                                    // }

                                    // Otherwise allow switching
                                    setSelectedPlan(product);
                                  }}
                                >

                                  <div className="label">{product.name}</div>
                                  {/* <div className="price">
                                  {new Intl.NumberFormat('en-US', {
                                    style: 'currency',
                                    currency: product.unit_price.currency_code,
                                  }).format(Number.parseFloat(product.unit_price.amount) / 100)}
                                </div> */}

                                  <div className="">
                                    {discountPercentage > 0
                                      ? (
                                          <>
                                            <div className="price">
                                              {new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: product.unit_price.currency_code,
                                              }).format(discountedPrice)}
                                            </div>
                                            <div className="original-price">
                                              {new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: product.unit_price.currency_code,
                                              }).format(originalPrice)}
                                            </div>
                                          </>
                                        )
                                      : (
                                          <div className="price">
                                            {new Intl.NumberFormat('en-US', {
                                              style: 'currency',
                                              currency: product.unit_price.currency_code,
                                            }).format(originalPrice)}
                                          </div>
                                        )}
                                  </div>

                                  {product.description && (
                                    <div className="sublabel">{product.description}</div>
                                  )}
                                </button>
                              );
                            })}
                          </div>
                        )}
                  {products.length !== 0
                    && (
                      <div className="btn-div">
                        <button
                          className="submitbtn"
                          type="button"
                          onClick={async () => {
                            if (!userId) {
                              toast.info('Subscribe first to unlock this.');
                              // toast.info('You need to subscribe before proceeding.');
                              router.push('/exploring');
                              return;
                            }

                            if (platform !== 'PADDLE') {
                              toast.info('You have purchased from the app, so you can not purchase from here');
                              return;
                            }

                            if (!Paddle) {
                              toast.error('Paddle not loaded');
                              return;
                            }

                            const currentPaddleSubId = subscriptionid;
                            const activePriceId = subscriptionPriceid;
                            const selectedPriceId = selectedPlan?.id;

                            // If already subscribed to this plan
                            if (activePriceId === selectedPriceId) {
                              toast.info(`You're already subscribed to the ${selectedPlan?.name} plan.`);
                              return;
                            }

                            // If user has an active Paddle subscription and is switching plans
                            if (currentPaddleSubId) {
                              try {
                                const res = await fetch(`${API}/subscriptions/${currentPaddleSubId}/cancel`, {
                                  method: 'POST',
                                  headers: {
                                    'Authorization': `Bearer ${token2}`,
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify({
                                    effective_from: 'immediately',
                                  }),
                                });

                                if (!res.ok) {
                                  const err = await res.json();
                                  console.error('Cancel error:', err);
                                  toast.error('Failed to cancel existing subscription');
                                  return;
                                }
                                // toast.success('Previous subscription cancelled');
                              } catch (err) {
                                console.error('Cancel error:', err);
                                toast.error('Something went wrong while canceling.');
                                return;
                              }
                            }

                            const appUserEnv = process.env.NEXT_PUBLIC_APP_USER_ID || '';
                            const appUserIdStr = `${appUserEnv}${userId}`;

                            const matchedDiscount = discounts.find(d =>
                              d.restrict_to.includes(selectedPlan?.id || ''),
                            );

                            customWindow.Paddle?.Checkout.open({
                              items: [{ priceId: selectedPlan?.id, quantity: 1 }],
                              settings: {
                                displayMode: 'overlay',
                              },
                              discountCode: matchedDiscount?.code,
                              customData: {
                                // app_user_id: "local_265",
                                app_user_id: appUserIdStr,
                                // email: "<EMAIL>",
                                email,
                              },
                              customer: {
                                // email: "<EMAIL>",
                                email,
                                // address: {
                                //   countryCode: 'US',
                                //   postalCode: '10021',
                                //   region: 'New York',
                                //   city: 'New York',
                                //   firstLine: '4050 Jefferson Plaza, 41st Floor',
                                // },
                              },
                            });
                          }}

                        >
                          Continue
                        </button>
                      </div>
                    )}

                  {subscriptionid && (
                    <div
                      className="cancel-subscription"
                      role="button"
                      tabIndex={0}
                      onClick={cancelSubscription}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          cancelSubscription();
                        }
                      }}
                    >
                      Cancel Subscription
                    </div>
                  )}

                </div>
              </div>

              {/* <div className="bear-subscription-animation">
              <SubscriptionBear action={1} emotion={1} />
            </div> */}
              <div className="bear-section">
                <div className="bear-subscription-animation">
                  <SubscriptionBear
                    action={4}
                    emotion={1}
                    outfitLevel={4}
                    darkMode={false}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default Subscription;
