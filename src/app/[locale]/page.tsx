import type { Metadata } from 'next';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Countdown from '@/components/Countdown';
import FramerBear from '@/components/FramerBear';
import Header from '@/components/Header';
import { getSEO } from '@/libs/getSEO';

export async function generateMetadata(): Promise<Metadata> {
  const seo = await getSEO('/');
  if (!seo) {
    return {};
  }

  return {
    title: seo.metaTitle,
    description: seo.metaDescription,
    keywords: [...seo.focusKeywords, ...(seo.extraKeywords || [])],
    openGraph: {
      title: seo.ogTitle,
      description: seo.ogDescription,
      images: [
        {
          url: seo.ogImageUrl,
          alt: seo.ogTitle,
        },
      ],
    },
  };
}

export default function HomePage() {
  const t = useTranslations('HomePage');

  return (
    <>
      <div className="hero-section">
        <Header />
        <div className="parent-count">
          <Countdown />
          <div className="count-title">
            <div>
              {t('countdowntitle')}
            </div>
            <div>
              {t('countdownsubtitle')}
            </div>
          </div>
        </div>

        <div className="bear-animation">
          <FramerBear action={1} emotion={1} />
        </div>
        <div className="hero-content">
          <p className="hero-title">
            <span style={{ color: '#e16758' }}>dojowell </span>
            {' '}
            –
            {' '}
            {t('title')}
          </p>
          <p className="join-desc">{t('description')}</p>
          <Link type="button" href="exploring" className="btndiv justify-center d-flex align-center">{t('btn')}</Link>
        </div>
      </div>
    </>
  );
}
