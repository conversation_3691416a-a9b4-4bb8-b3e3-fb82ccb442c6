'use client';

// import Cookies from 'js-cookie';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import SubscribeModal from '@/components/SubscribeModal';
import { apiUrls, QUIZ_BASE_URL } from '@/utils/AppConfig';
import './quiz.css';

const QuizBearAnimation = dynamic(() => import('@/components/QuizBearAnimation'), { ssr: false });

// type ScenarioAnswer = string | null;
// type RatingAnswer = {
//   rating: number;
//   followUpChoice: string | null;
//   otherText: string | null;
// } | null;

// type QuizAnswer = {
//   index: number;
//   id: string;
//   type: 'scenario' | 'rating';
//   category: string;
//   question: string;
//   answer: ScenarioAnswer | RatingAnswer;
// };

type RawQuizResult = Record<string, [string, number][]>;

export default function QuizPage() {
  const [subscribeModal, setSubscribeModal] = useState(false);
  // const [showSubscribeBanner, setShowSubscribeBanner] = useState(false);
  // const [isSubscribed, setIsSubscribed] = useState<boolean | null>(null);

  useEffect(() => {
    const script = document.createElement('script');
    script.src = '/quiz.js';
    script.async = true;
    document.body.appendChild(script);

    const params = new URLSearchParams(window.location.search);
    const userIdParam = params.get('id');
    const userId = userIdParam ? Number.parseInt(userIdParam, 10) : null;

    const handleQuizComplete = (e: Event) => {
      const customEvent = e as CustomEvent<RawQuizResult>;
      const resultData = customEvent.detail;

      if (!userId || Number.isNaN(userId)) {
        console.error('Invalid or missing user_id in URL');
        return;
      }

      const transformCategory = (items: [string, number][]): string[] =>
        items.map(([label]) => label);

      const answer_values = {
        attitudinal: transformCategory(resultData.attitudinal || []),
        creative: transformCategory(resultData.creative || []),
        experiential: transformCategory(resultData.experiential || []),
      };

      const payload = {
        user_id: userId,
        answer_values,
      };

      fetch(`${QUIZ_BASE_URL}${apiUrls.create_quiz}`, {
        // fetch('http://192.168.29.234:8484/web/v1/web_quiz_values/add_quiz_values', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })
        .then(async (res) => {
          const data = await res.json();
          if (data.success) {
            toast.success('Quiz values submitted successfully!');
          } else {
            toast.error(data.message || 'Submission failed!');
          }
        })
        .catch((error) => {
          console.error('Failed to send quiz data:', error);
        });
    };

    document.addEventListener('quiz:complete', handleQuizComplete);
    // document.addEventListener('quiz:complete', e => console.log('Caught from console:', e));

    // MutationObserver for showing banner when results are visible
    // const resultsScreenNode = document.getElementById('results-container');
    // const observer = new MutationObserver(() => {
    //   const resultsScreen = document.getElementById('results-container');
    //   if (resultsScreen && !resultsScreen.classList.contains('hidden')) {
    //     setShowSubscribeBanner(true);
    //   } else {
    //     setShowSubscribeBanner(false);
    //   }
    // });

    // if (resultsScreenNode) {
    //   observer.observe(resultsScreenNode, {
    //     attributes: true,
    //     attributeFilter: ['class'],
    //   });
    // }

    return () => {
      document.body.removeChild(script);
      document.removeEventListener('quiz:complete', handleQuizComplete);
      // observer.disconnect();
    };
  }, []);

  // const [bearAction, setBearAction] = useState(0);
  // const [bearEmotion, setBearEmotion] = useState(0);

  // useEffect(() => {
  //   const isSubCookie = Cookies.get('isSubscribe');
  //   if (isSubCookie === 'false') {
  //     setIsSubscribed(false);
  //   } else if (isSubCookie === 'true') {
  //     setIsSubscribed(true);
  //   } else {
  //     setIsSubscribed(null); // unknown
  //   }
  // }, []);

  return (
    <>
      <div className="page-container">
        <Header />
        {/* INTRO SCREEN */}
        <div className="intro-parent">
          <div className="intro-screen fade" id="intro-screen">
            <div className="intro-wrapper">
              <div className="intro-content">
                <h1>
                  Welcome to the
                  {' '}
                  <span className="brand">dojowell</span>
                  {' '}
                  values discovery quiz
                </h1>
                <p>
                  This quiz helps you explore important aspects of your life, guiding you to find meaning through purpose-driven actions and habits. It generally takes about
                  {' '}
                  <strong>10–15 minutes</strong>
                  {' '}
                  to complete.
                </p>
                <p>Please avoid distractions and take your time to answer each question thoughtfully.</p>
                <div className="intro-agreement">
                  <label>
                    <input className="agree-check" type="checkbox" id="agree-checkbox" />
                    I understand and agree to proceed.
                  </label>
                </div>
                <button type="button" className="intro-btn" id="start-quiz" disabled>Start Quiz</button>
              </div>
              <div className="intro-character">
                <div className="bear-quiz-animation">
                  <QuizBearAnimation action={0} emotion={1} />
                </div>
              </div>
            </div>
          </div>

          {/* QUIZ CONTAINER */}
          <div className="quiz-container hidden fade" id="quiz-container">
            {/* <div className="quiz-header">
            <button type="button" className="back-btn" id="back-btn" disabled>&larr; Back</button>
            <h2 className="quiz-title">DojoWell Values Discovery Quiz</h2>
          </div> */}
            <div className="quiz-header">
              <h2 className="quiz-title">dojoWell values discovery quiz</h2>
            </div>
            <div className="progress-bar-container">
              <div className="progress-bar">
                <div className="progress-fill" id="progress-fill" style={{ width: '0%' }}></div>
              </div>
              <div className="progress-text" id="progress-text">0% Completed</div>
            </div>
            <div className="question-area" id="question-area"></div>
            <div className="quiz-footer" id="quiz-footer">
              <button type="button" className="back-btn" id="back-btn" disabled>Back</button>
              {/* <button type="button" className="back-btn" id="back-btn" disabled>&larr; Back</button> */}
            </div>

          </div>

          {/* ANALYZING SCREEN */}
          <div className="analyzing-screen hidden fade" id="analyzing-screen">
            <h2>Please Wait</h2>
            <p>We are analyzing and calculating your personalized values...</p>
            <div className="loader"></div>
          </div>

          {/* RESULTS SCREEN */}
          <div className="results-container hidden fade" id="results-container">
            <h2>your top values</h2>
            <div className="results-content" id="results-content"></div>
            <button type="button" className="retake-btn" id="retake-quiz">retake quiz</button>

          </div>
        </div>

        {/* {showSubscribeBanner && isSubscribed === false && (
          <div className="subscribe-quiz-banner">
            <div className="subscribe-parent">
              <div className="text-area">
                <p className="subscribe-text">
                  your core values are clear.
                </p>
                <p className="subscribe-description">
                  Ready to bring them to life inside the app?
                  <div className="icon">
                    <img src="/assets/images/leaf.png" alt="audio icon" />
                  </div>
                  Subscribe today and get 10% off!
                </p>
              </div>
              <div className="btn-area">
                <button
                  className="subscription-btn"
                  onClick={() => setSubscribeModal(true)}
                >
                  unlock full journey
                </button>
              </div>
            </div>
          </div>
        )} */}

        <Footer />
      </div>

      {subscribeModal && (
        <SubscribeModal
          isOpen={subscribeModal}
          onClose={() => setSubscribeModal(false)}
        />
      )}
    </>
  );
}
