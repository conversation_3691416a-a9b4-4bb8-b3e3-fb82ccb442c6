/****************************************
 * GLOBAL STYLES / THEME
 ****************************************/
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* body {
  background-color: #eef2f3;
  color: #333;
} */

/* Webfont: DojowellBasic */
@font-face {
  font-family: 'DojowellBasic';
  src: url('/fonts/dojowell basic.eot');
  /* IE9 Compat Modes */
  src:
    url('/fonts/dojowell basic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/fonts/dojowell basic.woff') format('woff'),
    /* Modern Browsers */ url('/fonts/dojowell basic.woff2') format('woff2'),
    /* Modern Browsers */ url('/fonts/dojowell basic.ttf') format('truetype');
  /* Safari, Android, iOS */
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}

body {
  background: linear-gradient(to top, #e9f5fb, #d0e4f5);
  background-image: url('/assets/images/bg.png');
  /* optional mountain trees SVG */
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  /* font-family: 'Helvetica Neue', sans-serif; */
  color: #333;
  height: 100vh;
  font-family: 'DojowellBasic', Georgia;
}

button {
  cursor: pointer;
  font-size: 1rem;
}

.hidden {
  display: none;
}

/* FADE TRANSITIONS (slow) */
.fade {
  opacity: 1;
  transition: opacity 0.8s ease;
}

.fade-out {
  opacity: 0;
}

/****************************************
 * INTRO SCREEN
 ****************************************/

.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.intro-wrapper {
  flex: 1;
  display: flex;
  /* gap: 4rem; */
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  flex-wrap: wrap;
}

.intro-parent {
  flex: 1;
  display: flex;
  gap: 4rem;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  flex-wrap: wrap;
}

/* .intro-screen {
  flex: 1;
  max-width: min(90vw, 670px);
  padding: 40px;
  border-radius: 20px;
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.235) 100%);
   order: 1;
} */

.intro-content {
  flex: 1;
  max-width: min(90vw, 670px);
  padding: 40px;
  border-radius: 20px;
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.235) 100%);
  order: 1;
}

.intro-screen h1 {
  font-size: 40px;
  margin-bottom: 20px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  line-height: 130%;
}

.brand {
  color: #d9471f;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

.intro-screen p {
  margin: 10px 0;
  line-height: 1.6;
  color: #666869;
  font-size: 19px;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.intro-agreement {
  margin: 20px 0;
}

.intro-agreement label {
  display: flex;
  align-items: center;
  /* align-items: flex-start; */
  gap: 10px;
  font-size: 19px;
  color: #666869;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.agree-check {
  appearance: none;
  flex-shrink: 0;
  flex-grow: 0;
  -webkit-appearance: none;
  width: 22px;
  height: 22px;
  border: 2px solid #c2c3c5;
  border-radius: 6px;
  background-color: transparent;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.agree-check:checked {
  background-color: #c2c3c5;
  border-color: #c2c3c5;
}

.agree-check:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 7px;
  width: 6px;
  height: 11px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.intro-btn {
  border: 1px solid #e16758;
  color: #e16758;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50px;
  width: 300px;
  height: 75px;
  font-size: 25px;
  padding: 15px 25px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  cursor: pointer;
  margin-top: 20px;
}

.intro-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

.intro-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.intro-character {
  order: 2;
}

.bear-quiz-animation {
  width: 500px;
  height: 500px;
  overflow: hidden;
}

/* Responsive column layout on smaller screens */
@media screen and (max-width: 960px) {
  .intro-wrapper {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
  }

  .intro-content {
    order: 2;
    padding: 15px;
  }

  .intro-character {
    order: 1;
  }

  .bear-quiz-animation {
    width: 400px;
    height: 400px;
  }

  .intro-btn {
    cursor: pointer;
    width: 200px;
    height: 65px;
    font-size: 22px;
  }
}

@media screen and (max-width: 400px) {
  .bear-quiz-animation {
    width: 250px;
    height: 250px;
  }

  .intro-screen h1 {
    font-size: 30px;
  }

  .intro-screen p {
    font-size: 17px;
  }
}

/****************************************
 * QUIZ CONTAINER
 ****************************************/

/* .quiz-btn {
  display: inline-block;
  margin-top: 30px;
  padding: 12px 30px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 25px;
  transition:
    background-color 0.3s,
    transform 0.3s;
}
.quiz-btn:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-2px);
}

.quiz-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
} */

.quiz-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.quiz-footer .quiz-btn {
  background-color: transparent;
  border: 1.5px solid #e16758;
  color: #e16758;
  border-radius: 50px;
  padding: 12px 45px;
  font-size: 22px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  cursor: pointer;
}

/* .quiz-footer .quiz-btn:hover:not(:disabled) {
  background-color: #E16758;
  color: #fff;
  transform: translateY(-2px);
} */

.quiz-footer .quiz-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quiz-footer .quiz-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

@media (max-width: 500px) {
  .quiz-footer {
    /* flex-direction: column; */
    align-items: center;
  }

  .quiz-footer .quiz-btn,
  .back-btn {
    width: 100%;
    max-width: 150px;
    font-size: 20px;
  }

  .back-btn {
    padding: 6px 25px !important;
    font-size: 20px !important;
  }

  .back-btn:hover {
    box-shadow: 0px 4px 10px 0px #e1675880;
    background-color: #e16758;
    color: #ffffff;
  }

  .quiz-footer .quiz-btn {
    padding: 6px 25px;
    font-size: 20px;
  }

  input[type='radio'] {
    appearance: none;
    flex-shrink: 0;
    flex-grow: 0;
    -webkit-appearance: none;
    width: 20px !important;
    height: 20px !important;
    border: 1px solid #c2c3c5;
    /* Unchecked border */
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
  }

  /* Custom middle dot */
  input[type='radio']:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    background-color: #e16758;
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
}

.quiz-container {
  order: 1;
}

.quiz-container {
  /* max-width: 800px; */
  /* margin: 50px auto; */
  /* background-color: #fff; */
  /* padding: 40px; */
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); */
  /* border-radius: 10px; */
  position: relative;
  flex: 1;
  max-width: min(90vw, 670px);
  padding: 40px;
  border-radius: 20px;
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.235) 100%);
}

.quiz-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-btn {
  background-color: transparent;
  border: 1.5px solid #e16758;
  color: #e16758;
  border-radius: 50px;
  padding: 12px 45px;
  font-size: 22px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  cursor: pointer;
}

.back-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

.back-btn:disabled {
  transform: translateY(-2px);
  opacity: 0.5;
}

.quiz-title {
  margin: 0 auto;
  color: #e16758;
  text-align: center;
  font-size: 2rem;
  flex: 1;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

/* PROGRESS BAR */
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background-color: #0000001f;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background-color: #e16758;
  transition: width 0.5s ease-in-out;
}

.progress-text {
  font-size: 1.2rem;
  color: #666869a6;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

/* QUESTION STYLES */
.question-area {
  margin-top: 20px;
  min-height: 220px;
}

.question {
  margin-bottom: 25px;
}

.question p {
  font-size: 1.3em;
  margin-bottom: 15px;
  color: #666869;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
}

.options,
.rating-options,
.follow-up-options {
  list-style-type: none;
  padding: 0;
}

.options li,
.rating-options li,
.follow-up-options li {
  margin-bottom: 15px;
  transition:
    transform 0.3s,
    background-color 0.3s;
  border-radius: 5px;
}

/* .options li:hover,
.rating-options li:hover,
.follow-up-options li:hover {
  background-color: #f0f8ff;
  transform: translateX(5px);
} */

/* .option-selected {
  background-color: #d1ecf1;
  border-left: 5px solid #0c5460;
} */

label {
  cursor: pointer;
  display: flex;
  align-items: center;
  /* align-items: flex-start; */
  gap: 10px;
  color: #666869;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 20px;
}

/* input[type='radio'] {
  accent-color: #007bff;
} */
input[type='radio'] {
  flex-shrink: 0;
  flex-grow: 0;
  appearance: none;
  -webkit-appearance: none;
  width: 24px;
  height: 24px;
  border: 2px solid #c2c3c5;
  /* Unchecked border */
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

input[type='radio']:checked {
  /* border-color: #E16758; */
  border: 2px solid #e16758;
}

/* Custom middle dot */
input[type='radio']:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 13px;
  height: 13px;
  background-color: #e16758;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* FOLLOW-UP */
#other-input {
  margin-top: 15px;
}

#other-input input {
  width: 100%;
  padding: 8px 10px;
  border: 2px solid #007bff;
  border-radius: 5px;
  transition: border-color 0.3s;
}

#other-input input:focus {
  border-color: #0056b3;
  outline: none;
}

/* Responsive column layout on smaller screens */
@media screen and (max-width: 768px) {
  .quiz-container {
    order: 2;
  }

  .intro-character {
    order: 1;
  }

  .bear-quiz-animation {
    width: 300px;
    height: 300px;
  }

  .intro-btn {
    cursor: pointer;
    width: 200px;
    height: 65px;
    font-size: 22px;
  }

  .quiz-container {
    padding: 15px;
  }
}

@media screen and (max-width: 400px) {
  .bear-quiz-animation {
    width: 250px;
    height: 250px;
  }

  label {
    font-size: 18px;
  }

  .question p {
    font-size: 20px;
  }

  .quiz-title {
    font-size: 1.8rem;
  }

  .progress-text {
    font-size: 1rem;
  }
}

/****************************************
 * ANALYZING SCREEN
 ****************************************/
.analyzing-screen {
  /* max-width: 700px; */
  min-width: min(90vw, 970px);
  margin: 60px auto;
  /* background-color: #fff; */
  padding: 40px;
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); */
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.235) 100%);
  border-radius: 30px;
  text-align: center;
}

.analyzing-screen h2 {
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  font-size: 30px;
}

.analyzing-screen p {
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 20px;
  color: #666869;
  margin-top: 10px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid #e16758;
  width: 50px;
  height: 50px;
  margin: 20px auto 0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/****************************************
 * RESULTS SCREEN
 ****************************************/
.results-container {
  /* max-width: 800px; */
  min-width: min(90vw, 1200px);
  margin: 40px auto;
  background-color: #fff;
  padding: 40px;
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); */
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.235) 100%);
  border-radius: 30px;
  border-radius: 10px;
  text-align: center;
}

.results-container h2 {
  font-size: 34px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  color: #e16758;
}

/* .results-content {
  margin-top: 25px;
  animation: fadeIn 1s forwards;
  display: flex;
  gap: 4rem;
}

.results-category {
  background-color: #fafafa;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
} */

.results-content {
  margin-top: 25px;
  animation: fadeIn 1s forwards;
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;

  /* Optional: makes it responsive */
}

/* .results-category {
  padding: 23px;
  border: 1px solid #C2C3C5;
  border-radius: 10px;
  margin-bottom: 20px;
  flex: 1;
  min-width: 250px;
} */

.results-category {
  flex: 1 1 300px;
  max-width: 100%;
  padding: 23px;
  border: 1px solid #c2c3c5;
  border-radius: 10px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.results-category h3 {
  margin-bottom: 12px;
  font-size: 30px;
  color: #666869;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  text-align: left;
}

.results-category ul {
  list-style-type: disc;
  padding-left: 20px;
  text-align: left;
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
}

.results-category ul li {
  /* font-family: Optima medium; */
  font-family: 'DojowellBasic', Georgia;
  font-size: 19px;
  color: #666869;
  margin-bottom: 10px;
}

.results-category ul li:last-child {
  margin-bottom: 0;
}

.retake-btn {
  border: 1px solid #e16758;
  color: #e16758;
  border-radius: 50px;
  padding: 15px 25px;
  margin-top: 30px;
  /* font-family: Optima Bold; */
  font-family: 'DojowellBasic', Georgia;
  font-weight: 700;
  cursor: pointer;
  width: 300px;
  font-size: 25px;

  height: 75px;
}

.retake-btn:hover {
  box-shadow: 0px 4px 10px 0px #e1675880;
  background-color: #e16758;
  color: #ffffff;
}

/* 📱 Mobile optimizations */
@media screen and (max-width: 768px) {
  .results-container {
    padding: 25px 15px;
  }

  .results-container h2 {
    font-size: 26px;
  }

  .results-category h3 {
    font-size: 22px;
  }

  .results-category ul li {
    font-size: 16px;
  }

  .retake-btn {
    cursor: pointer;
    width: 200px;
    height: 65px;
    font-size: 22px;
  }
}

@media screen and (max-width: 480px) {
  .results-content {
    gap: 1rem;
  }

  .results-category {
    flex: 1 1 100%;
  }

  .results-container h2 {
    font-size: 25px;
  }

  .results-category h3 {
    font-size: 20px;
  }

  .results-category ul li {
    font-size: 15px;
  }
}

.all-qa {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #ccc;
}

.qa-item {
  margin-bottom: 1rem;
}

.qa-item p {
  margin: 0.25rem 0;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
