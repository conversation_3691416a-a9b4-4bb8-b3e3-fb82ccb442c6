import type { Metadata } from 'next';
import { getSEO } from '@/libs/getSEO';
import QuizPage from './QuizClient';

export function generateMetadata(): Metadata {
  const seo = getSEO('/quiz');

  if (!seo) {
    return {};
  }

  return {
    title: seo.metaTitle,
    description: seo.metaDescription,
    keywords: [...seo.focusKeywords, ...(seo.extraKeywords || [])],
    openGraph: {
      title: seo.ogTitle,
      description: seo.ogDescription,
      images: [
        {
          url: seo.ogImageUrl,
          alt: seo.ogTitle,
        },
      ],
    },
  };
}

export default function FaqPage() {
  return <QuizPage />;
}
