'use client';
import type { CollapseProps } from 'antd';
import { useMessages, useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
// import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
// import SupportForm from '@/components/SupportForm';
import arrow from '../../../../public/assets/images/arrow.png';

// const Tabs = ({ activeTab, setActiveTab }: { activeTab: string; setActiveTab: (tab: string) => void }) => {
//   const t = useTranslations('Faq');
//   const tabs = [
//     t('category1'),
//     t('category2'),
//     t('category3'),
//     t('category4'),
//     t('category5'),
//   ];

//   return (
//     <div className="tab-container">
//       {tabs.map(tab => (
//         <button
//           key={tab}
//           className={`tab-btn ${activeTab === tab ? 'active' : ''}`}
//           onClick={() => setActiveTab(tab)}
//           type="button"
//         >
//           {tab}
//         </button>
//       ))}
//     </div>
//   );
// };

const FaqsClient = () => {
  // const [activeTab, setActiveTab] = useState<string>('About DojoWell');
  const [activeQue, setActiveQue] = useState<null | number>(null);
  // const [showForm, setShowForm] = useState(false);
  // const formRef = useRef<HTMLDivElement | null>(null);

  const t = useTranslations('Faq');

  const messages = useMessages();
  const faqItems = (messages as any).Faq.items as Record<string, { question: string; answer: string }>;

  const items: CollapseProps['items'] = Object.entries(faqItems).map(([key, value]) => ({
    key,
    label: value.question,
    children: <p>{value.answer}</p>,
  }));

  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkWidth = () => {
      setIsMobile(window.innerWidth < 1200);
    };

    checkWidth(); // run on mount

    window.addEventListener('resize', checkWidth);
    return () => window.removeEventListener('resize', checkWidth);
  }, []);

  // useEffect(() => {
  //   if (showForm && formRef.current) {
  //     formRef.current.scrollIntoView({ behavior: 'smooth' });
  //   }
  // }, [showForm]);

  return (
    <>
      <div className="main-div">
        <Header />
        <div className="main-long-container">
          <div className="content-long-container">
            <div className="faq-page">
              <p className="support-title">{t('title')}</p>
              <p className="join-desc mt-3">{t('subtitle')}</p>
            </div>
            {/* <Tabs activeTab={activeTab} setActiveTab={setActiveTab} /> */}

            <div className={isMobile ? 'faq-container' : ''}>
              {
                items.map((question, i) => {
                  return (
                    <div
                      className="faq-accordian"
                      style={{ width: isMobile ? '100%' : '', minHeight: isMobile ? 'auto' : '' }}
                      key={question.key}
                      onClick={() => setActiveQue(activeQue === i ? null : i)}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          setActiveQue(activeQue === i ? null : i);
                        }
                      }}
                    >
                      <div className="question-container d-flex justify-between">
                        <div className="questions">{question.label}</div>
                        <div className="arrow">
                          <Image src={arrow} alt="arrow" className={activeQue === i ? 'arrow-icon rotated' : 'arrow-icon'} />
                        </div>
                      </div>
                      <div className={`answer-container ${activeQue === i ? 'open' : ''}`}>
                        <div className="answer">{question.children}</div>
                      </div>
                    </div>
                  );
                })
              }
            </div>
            <p className="titletext mt-5">
              {t('faqlink')}
              {' '}
              <Link
                href="/support"
                // onClick={() => setShowForm(true)}
                style={{ color: '#E27063', textDecoration: 'underline', cursor: 'pointer' }}
              >
                {t('faqlinktext')}
              </Link>

              {/* {showForm && (
                <div className="mt-5" ref={formRef}>
                  <SupportForm />
                </div>
              )} */}
            </p>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default FaqsClient;
