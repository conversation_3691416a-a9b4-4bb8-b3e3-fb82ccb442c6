'use client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import { apiUrls, BASE_URL } from '@/utils/AppConfig';
import line from '../../../../public/assets/images/line.png';

const Support = () => {
  const [formData, setFormData] = useState({
    fullname: '',
    email: '',
    message: '',
    category: '',
  });

  const [confirmMessage, setConfirmMessage] = useState('');

  const t = useTranslations('Support');

  const formValidation = (): boolean => {
    let isValid = true;
    // const errors = {
    //   fname: '',
    //   lname: '',
    //   email: '',
    //   category: '',
    // };

    toast.dismiss();

    if (!formData.fullname.trim()) {
      // errors.fname = 'Full name is required';
      toast.error('Full name is required', {
        toastId: 'form-error',
        position: 'top-right',
        autoClose: 3000,
      });
      isValid = false;
    }

    if (!formData.email.trim()) {
      // errors.email = 'Email is required';
      toast.error('Email is required', {
        toastId: 'form-error',
        position: 'top-right',
        autoClose: 3000,
      });
      isValid = false;
    } else if (!/\S[^\s@]*@\S+\.\S+/.test(formData.email)) {
      // errors.email = 'Enter a valid email';
      toast.error('Enter a valid email', {
        toastId: 'form-error',
        position: 'top-right',
        autoClose: 3000,
      });
      isValid = false;
    }

    if (!formData.category) {
      toast.error('Please select a category', {
        toastId: 'form-error',
        position: 'top-right',
        autoClose: 3000,
      });
      isValid = false;
    }

    if (!formData.message.trim()) {
      // errors.lname = 'Message is required';
      toast.error('Message is required', {
        toastId: 'form-error',
        position: 'top-right',
        autoClose: 3000,
      });
      isValid = false;
    }

    return isValid;
  };

  const createSupport = async () => {
    const isFormValid = formValidation();
    if (!isFormValid) {
      return;
    }

    try {
      const response = await fetch(`${BASE_URL}${apiUrls.create_support}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: formData.fullname,
          email_address: formData.email,
          message: formData.message,
          category: formData.category,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setFormData({
          fullname: '',
          email: '',
          message: '',
          category: '',
        });
        setConfirmMessage('Thank you — we’ll get back to you soon.');
        setTimeout(() => setConfirmMessage(''), 5000);
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Something went wrong!', {
        position: 'top-right',
        autoClose: 3000,
      });
      console.error('Error:', error);
    }
  };

  return (
    <>
      <div className="main-div">
        <Header />
        <div className="main-long-container">
          <div className="content-long-container">
            <div className="support-page">
              <p className="support-title" style={{ color: '#E16758' }}>{t('title')}</p>
              <p className="support-title">{t('subtitle')}</p>
              <p className="join-desc mt-3">{t('description')}</p>
            </div>
            <div className="support-page-secondary">
              <p className="support-title" style={{ maxWidth: '1080px' }}>{t('faqtitle')}</p>
              <Link href="faqs"><button className="btndiv mt-10" type="button"> View FAQ</button></Link>
              <Image src={line} alt="line" className="mt-10" />
            </div>
            <div>
              <div id="support-form-section">
                <div className="support-form">
                  <p className="support-title">
                    <span style={{ color: '#E16758' }}>
                      {t('contacttitle')}
                      {' '}
                    </span>
                    {' '}
                    {t('contactspan')}
                  </p>
                  <input className="inputfile w-50 mb-0" value={formData.fullname} placeholder="Enter full name" onChange={e => setFormData({ ...formData, fullname: e.target.value })} />
                  <input className="inputfile w-50 mb-0" value={formData.email} placeholder="Enter email" onChange={e => setFormData({ ...formData, email: e.target.value })} />

                  <div className="inputfile w-100 mb-0">
                    <select
                      className={`dropdown-select ${formData.category === '' ? 'placeholder-colour' : ''
                      }`}
                      value={formData.category}
                      onChange={e => setFormData({ ...formData, category: e.target.value })}
                    >
                      <option value="" disabled className="">Select Category</option>
                      <option value="General Question">General Question</option>
                      <option value="Bug">Bug</option>
                      <option value="Payment Issue">Payment Issue</option>
                      <option value="Feature Request">Feature Request</option>
                      <option value="Feedback">Feedback</option>
                    </select>
                  </div>

                  <textarea className="textarea mt-1 mb-0" value={formData.message} placeholder="Enter your message here" onChange={e => setFormData({ ...formData, message: e.target.value })} />
                  <p className="confirm">{confirmMessage}</p>
                  <button className="btndiv" onClick={() => createSupport()} type="button">
                    {' '}
                    {t('contactbtn')}
                  </button>
                </div>
              </div>
              <p className="join-desc mt-5" style={{ fontSize: '30px' }}>
                {t('contactemail')}
                <span style={{ color: '#E16758', fontFamily: '"DojowellBasic", Georgia' }}>
                  {' '}
                  {t('email')}
                </span>
              </p>
              <p className="join-desc mt-3" style={{ color: '#82858D', fontSize: '23px', marginBottom: '50px' }}>{t('contactfooterlink')}</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default Support;
