import type { Metadata } from 'next';
import { getSEO } from '@/libs/getSEO';
import Support from './SupportClient';

export function generateMetadata(): Metadata {
  const seo = getSEO('/support');

  if (!seo) {
    return {};
  }

  return {
    title: seo.metaTitle,
    description: seo.metaDescription,
    keywords: [...seo.focusKeywords, ...(seo.extraKeywords || [])],
    openGraph: {
      title: seo.ogTitle,
      description: seo.ogDescription,
      images: [
        {
          url: seo.ogImageUrl,
          alt: seo.ogTitle,
        },
      ],
    },
  };
}

export default function FaqPage() {
  return <Support />;
}
