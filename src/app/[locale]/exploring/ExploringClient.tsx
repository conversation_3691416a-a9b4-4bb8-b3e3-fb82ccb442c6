'use client';
import { useState } from 'react';
import BearAnimation from '@/components/BearAnimation';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import MainAnimation from '@/components/MainAnimation';
import ComingSoon from '../comingsoon/page';

const ExploringClient = () => {
  const [bearAction, setBearAction] = useState(0);
  const [bearEmotion, setBearEmotion] = useState(0);
  const [bearPosition, setBearPosition] = useState(28);
  const [overflow, setOverflow] = useState('hidden');

  return (
    <>
      <div className="main-div">
        <Header />
        {/* <div className="exploring-container" style={{ overflow, height: overflow === 'auto' ? 'auto' : "100vh" }}> */}
        <div className="exploring-container" style={{ overflow, height: overflow === 'auto' ? 'auto' : `calc(100vh - 80px)` }}>
          <MainAnimation setBearAction={setBearAction} setBearEmotion={setBearEmotion} setBearPosition={setBearPosition} bearPosition={bearPosition} setOverflow={setOverflow} />
          <div className="bear-animation-explore" style={{ left: `${bearPosition}%` }}>
            <BearAnimation action={bearAction} emotion={bearEmotion} />
          </div>
          <ComingSoon />
        </div>
        <Footer />
      </div>
    </>
  );
};

export default ExploringClient;
