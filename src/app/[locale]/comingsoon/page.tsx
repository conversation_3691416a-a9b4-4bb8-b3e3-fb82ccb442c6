'use client';
// import { type TokenResponse, useGoogleLogin } from '@react-oauth/google';
// import Cookies from 'js-cookie';
import { useLocale, useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
// import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { toast } from 'react-toastify';
import { apiUrls, BASE_URL } from '@/utils/AppConfig';

const ThankyouModal = dynamic(() => import('@/components/ThankyouModal'), { ssr: false });
const Wailtlist = dynamic(() => import('@/components/Wailtlist'), { ssr: false });
const SubscribeModal = dynamic(() => import('@/components/SubscribeModal'), { ssr: false });

const ComingSoon = () => {
  const [showModal, setShowModal] = useState(false);
  const [thankYouModal, setThankYouModal] = useState(false);
  const [subscribeModal, setSubscribeModal] = useState(false);
  const [formData, setFormData] = useState({
    fname: '',
    lname: '',
    email: '',
  });
  const [loading, setLoading] = useState(false);
  // const [showSubscribeBanner, setShowSubscribeBanner] = useState(true);

  // const router = useRouter();

  // useEffect(() => {
  //   const email = Cookies.get('user_email');
  //   if (email) {
  //     setShowSubscribeBanner(false);
  //   }
  // }, []);

  const { executeRecaptcha } = useGoogleReCaptcha();

  const t = useTranslations('Exploring');
  const locale = useLocale();

  const addMemberinList = async () => {
    if (!executeRecaptcha) {
      toast.error('reCAPTCHA is not ready');
      return;
    }

    try {
      setLoading(true);
      // ✅ Get reCAPTCHA token from frontend
      const token = await executeRecaptcha('submit_waitlist');
      // const geoRes = await fetch(`https://api.ipapi.com/api/check?access_key=********************************`);
      // const geoRes = await fetch(`https://api.ipapi.com/api/check?access_key=********************************`);
      const geoRes = await fetch(`https://ipwho.is/`);
      const geoData = await geoRes.json();

      const countryCode = `+${geoData.calling_code}`;
      // const countryCode = `+${geoData.location.calling_code}`;
      const countryName = geoData.country;
      // const countryName = geoData.country_name;

      const response = await fetch(`${BASE_URL}${apiUrls.add_to_waitlist}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: formData.fname,
          last_name: formData.lname,
          email_address: formData.email,
          country_code: countryCode,
          country_name: countryName,
          token,
          language: locale,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setShowModal(false);
        setThankYouModal(true);
        toast.success(data.message);
        setFormData({
          fname: '',
          lname: '',
          email: '',
        });
      } else {
        toast.error(data.message);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Something went wrong!');
      setLoading(false);
    }
  };

  // const login = useGoogleLogin({
  //   onSuccess: async (tokenResponse: TokenResponse) => {
  //     // console.log('Access Token:', tokenResponse.access_token);

  //     try {
  //       const res = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
  //         headers: {
  //           Authorization: `Bearer ${tokenResponse.access_token}`,
  //         },
  //       });

  //       const userInfo = await res.json();
  //       // console.log('User Info:', userInfo);

  //       if (!executeRecaptcha) {
  //         toast.error('reCAPTCHA is not ready');
  //         return;
  //       }

  //       // const token = await executeRecaptcha('submit_waitlist');
  //       // const geoRes = await fetch(`https://api.ipapi.com/api/check?access_key=********************************`);
  //       // const geoRes = await fetch(`https://api.ipapi.com/api/check?access_key=********************************`);
  //       const geoRes = await fetch(`https://ipwho.is/`);
  //       const geoData = await geoRes.json();
  //       // console.log('geoData', geoData.country_code);
  //       const countryCode = geoData.country_code;
  //       // const countryName = geoData.country_name;

  //       const response = await fetch(`${BASE_URL}${apiUrls.create_register_user}`, {
  //         method: 'POST',
  //         headers: {
  //           'Content-Type': 'application/json',
  //         },
  //         body: JSON.stringify({
  //           first_name: userInfo.given_name || '',
  //           last_name: userInfo.family_name || '',
  //           email_address: userInfo.email,
  //           country_code: countryCode,
  //           // country_name: countryName,
  //           // token: token,
  //         }),
  //       });

  //       const result = await response.json();
  //       if (result.success) {
  //         toast.success(result.message || 'Successfully registered!');
  //         // setThankYouModal(true);
  //         Cookies.set('user_email', result?.data?.email_address, { expires: 3650 });
  //         Cookies.set('user_id', result?.data?.id, { expires: 3650 });
  //         router.push('/subscription');
  //       } else {
  //         toast.error(result.message || 'Failed to register.');
  //       }
  //     } catch (error) {
  //       console.error('Google Login Error:', error);
  //       toast.error('Something went wrong during registration.');
  //     }
  //   },
  //   onError: (err) => {
  //     console.error('Google Login Failed', err);
  //     toast.error('Google Login Failed');
  //   },
  // });

  return (
    <div className="main-container">
      <div className="comingsoon-container">
        {/* <Image className="coming-logo" src={logo} alt="logo" width={438} height={116} />
        <CountdownTimer /> */}
        <p className="comingsoon-title">
          {t('title1')}
          {' '}
          <br />
          {/* <span style={{ color: '#E16758', marginLeft: '10px', marginRight: '10px' }}>
            {t('span')}
          </span>
          {t('title2')} */}
          {t('span')}
        </p>
        <p className="comingsoon-desc">
          {t('title2')}
        </p>
        {locale !== 'en' && (
          <button className="btndiv" type="button" onClick={() => setShowModal(true)}>
            {t('btn')}
          </button>
        )}
      </div>
      <Wailtlist
        ShowModal={showModal}
        formData={formData}
        setFormData={setFormData}
        setShowModal={setShowModal}
        sendOtp={addMemberinList}
        loading={loading}
      />
      <ThankyouModal
        thankYouModal={thankYouModal}
        setThankYouModal={setThankYouModal}
      />

      {/* {showSubscribeBanner && (
        <div className="subscribe-banner">
          <div className="subscribe-parent">
            <div className="text-area">
              <p className="subscribe-text">
                you’ve felt the shift. now begin the journey.
              </p>
              <p className="subscribe-desc">
                subscribe today to unlock your wellness path inside the dojoWell app.
              </p>
            </div>
            <div className="btn-area">
              <button
                className="subscribe-btn"
                // onClick={() => setSubscribeModal(true)}
                onClick={() => login()}
              >
                Subscribe & begin
              </button>
            </div>
          </div>
        </div>
      )} */}

      {/* <Footer /> */}

      {subscribeModal && (
        <SubscribeModal
          isOpen={subscribeModal}
          onClose={() => setSubscribeModal(false)}
        />
      )}

    </div>

  );
};

export default ComingSoon;
