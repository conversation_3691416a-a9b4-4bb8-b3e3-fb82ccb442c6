// src/app/layout.tsx
import { ToastContainer } from 'react-toastify';
// import FirstVisitPopup from '@/components/FirstVisitPopup';
import Providers from './providers';
import '../../public/fonts/style.css';
import '../styles/global.css';
import 'react-toastify/dist/ReactToastify.css';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
          {/* <FirstVisitPopup /> */}
          <ToastContainer />
        </Providers>
      </body>
    </html>
  );
}
